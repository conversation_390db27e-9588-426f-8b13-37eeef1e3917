<template>
  <div class="debug-container">
    <h2>用户数据调试页面</h2>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>localStorage 数据</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshData">刷新</el-button>
      </div>
      
      <h4>已注册用户：</h4>
      <pre>{{ JSON.stringify(registeredUsers, null, 2) }}</pre>
      
      <h4>当前用户：</h4>
      <pre>{{ JSON.stringify(currentUser, null, 2) }}</pre>
    </el-card>
    
    <el-card style="margin-bottom: 20px;">
      <div slot="header">
        <span>快速注册测试用户</span>
      </div>
      
      <el-button type="primary" @click="createTestUser">创建测试用户</el-button>
      <p>将创建用户名: testuser, 密码: Test123456</p>
    </el-card>
    
    <el-card>
      <div slot="header">
        <span>快速登录测试</span>
      </div>
      
      <el-form :model="loginForm" style="max-width: 300px;">
        <el-form-item label="用户名">
          <el-input v-model="loginForm.username" />
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="loginForm.password" type="password" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testLogin">测试登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card style="margin-top: 20px;">
      <div slot="header">
        <span>操作</span>
      </div>
      
      <el-button type="danger" @click="clearAllData">清除所有数据</el-button>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'Debug',
  data() {
    return {
      registeredUsers: [],
      currentUser: {},
      loginForm: {
        username: '',
        password: ''
      }
    }
  },
  mounted() {
    this.refreshData()
  },
  methods: {
    refreshData() {
      this.registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]')
      this.currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
    },
    createTestUser() {
      const testUser = {
        id: Date.now(),
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Test123456',
        realName: '测试用户',
        phone: '13800138000',
        createdAt: new Date().toISOString()
      }
      
      const users = JSON.parse(localStorage.getItem('registeredUsers') || '[]')
      
      // 检查是否已存在
      const exists = users.find(u => u.username === testUser.username)
      if (exists) {
        this.$message.warning('测试用户已存在')
        return
      }
      
      users.push(testUser)
      localStorage.setItem('registeredUsers', JSON.stringify(users))
      
      this.$message.success('测试用户创建成功')
      this.refreshData()
    },
    testLogin() {
      if (!this.loginForm.username || !this.loginForm.password) {
        this.$message.warning('请输入用户名和密码')
        return
      }
      
      this.$store.dispatch('user/login', this.loginForm).then(() => {
        this.$message.success('登录成功')
        this.refreshData()
      }).catch(error => {
        this.$message.error('登录失败: ' + error.message)
      })
    },
    clearAllData() {
      localStorage.removeItem('registeredUsers')
      localStorage.removeItem('currentUser')
      this.$message.success('数据已清除')
      this.refreshData()
    }
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
}
</style>
