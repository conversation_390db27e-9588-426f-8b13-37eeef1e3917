<template>
  <div class="register-container">
    <h1>注册测试页面</h1>
    
    <el-button type="primary" @click="testClick">测试按钮</el-button>
    
    <el-form ref="testForm" :model="testForm" style="margin-top: 20px;">
      <el-form-item>
        <el-input v-model="testForm.username" placeholder="用户名" />
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'RegisterTest',
  data() {
    return {
      testForm: {
        username: ''
      }
    }
  },
  methods: {
    testClick() {
      console.log('测试按钮被点击')
      alert('测试按钮工作正常！')
    },
    handleSubmit() {
      console.log('提交按钮被点击')
      console.log('表单数据:', this.testForm)
      alert('提交功能正常！用户名：' + this.testForm.username)
    }
  }
}
</script>

<style scoped>
.register-container {
  padding: 50px;
  max-width: 500px;
  margin: 0 auto;
}
</style>
