<template>
  <div class="register-container">
    <el-form ref="registerForm" :model="registerForm" :rules="registerRules" class="register-form" auto-complete="on" label-position="left">

      <div class="title-container">
        <h3 class="title">用户注册</h3>
      </div>

      <el-form-item prop="username">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="username"
          v-model="registerForm.username"
          placeholder="用户名"
          name="username"
          type="text"
          tabindex="1"
          auto-complete="on"
          @blur="checkUsernameAvailable"
        />
      </el-form-item>

      <el-form-item prop="email">
        <span class="svg-container">
          <svg-icon icon-class="email" />
        </span>
        <el-input
          ref="email"
          v-model="registerForm.email"
          placeholder="邮箱"
          name="email"
          type="email"
          tabindex="2"
          auto-complete="on"
          @blur="checkEmailAvailable"
        />
      </el-form-item>

      <el-form-item prop="password">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input
          :key="passwordType"
          ref="password"
          v-model="registerForm.password"
          :type="passwordType"
          placeholder="密码"
          name="password"
          tabindex="3"
          auto-complete="on"
        />
        <span class="show-pwd" @click="showPwd">
          <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>

      <el-form-item prop="confirmPassword">
        <span class="svg-container">
          <svg-icon icon-class="password" />
        </span>
        <el-input
          :key="confirmPasswordType"
          ref="confirmPassword"
          v-model="registerForm.confirmPassword"
          :type="confirmPasswordType"
          placeholder="确认密码"
          name="confirmPassword"
          tabindex="4"
          auto-complete="on"
          @keyup.enter.native="handleRegister"
        />
        <span class="show-pwd" @click="showConfirmPwd">
          <svg-icon :icon-class="confirmPasswordType === 'password' ? 'eye' : 'eye-open'" />
        </span>
      </el-form-item>

      <el-form-item prop="realName">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="realName"
          v-model="registerForm.realName"
          placeholder="真实姓名"
          name="realName"
          type="text"
          tabindex="5"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="phone">
        <span class="svg-container">
          <svg-icon icon-class="phone" />
        </span>
        <el-input
          ref="phone"
          v-model="registerForm.phone"
          placeholder="手机号码"
          name="phone"
          type="tel"
          tabindex="6"
          auto-complete="on"
        />
      </el-form-item>

      <el-form-item prop="agreement">
        <el-checkbox v-model="registerForm.agreement">
          我已阅读并同意<a href="#" @click.prevent="showAgreement">《用户协议》</a>和<a href="#" @click.prevent="showPrivacy">《隐私政策》</a>
        </el-checkbox>
      </el-form-item>

      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:30px;" @click.native.prevent="handleRegister">注册</el-button>

      <div class="tips">
        <span>已有账号？</span>
        <router-link to="/login" class="link-type">立即登录</router-link>
      </div>

    </el-form>

    <!-- 用户协议对话框 -->
    <el-dialog title="用户协议" :visible.sync="agreementVisible" width="60%">
      <div class="agreement-content">
        <h4>1. 服务条款</h4>
        <p>欢迎使用我们的服务。通过访问和使用本服务，您同意遵守以下条款和条件。</p>
        
        <h4>2. 用户责任</h4>
        <p>用户应当对其账户信息的安全性负责，不得将账户信息泄露给第三方。</p>
        
        <h4>3. 隐私保护</h4>
        <p>我们承诺保护用户的隐私信息，不会未经授权向第三方披露用户信息。</p>
        
        <h4>4. 服务变更</h4>
        <p>我们保留随时修改或终止服务的权利，恕不另行通知。</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="agreementVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 隐私政策对话框 -->
    <el-dialog title="隐私政策" :visible.sync="privacyVisible" width="60%">
      <div class="privacy-content">
        <h4>1. 信息收集</h4>
        <p>我们收集您提供的个人信息，包括但不限于姓名、邮箱、手机号码等。</p>
        
        <h4>2. 信息使用</h4>
        <p>我们使用收集的信息来提供、维护和改进我们的服务。</p>
        
        <h4>3. 信息共享</h4>
        <p>除法律要求外，我们不会与第三方共享您的个人信息。</p>
        
        <h4>4. 数据安全</h4>
        <p>我们采用适当的技术和组织措施来保护您的个人信息安全。</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="privacyVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { register, checkUsername, checkEmail } from '@/api/user'
import { validEmail, validPhone } from '@/utils/validate'

export default {
  name: 'Register',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入用户名'))
      } else if (value.length < 3) {
        callback(new Error('用户名至少3个字符'))
      } else if (value.length > 20) {
        callback(new Error('用户名不能超过20个字符'))
      } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
        callback(new Error('用户名只能包含字母、数字和下划线'))
      } else {
        callback()
      }
    }

    const validateEmail = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入邮箱'))
      } else if (!validEmail(value)) {
        callback(new Error('请输入正确的邮箱格式'))
      } else {
        callback()
      }
    }

    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else if (value.length < 6) {
        callback(new Error('密码不能少于6个字符'))
      } else if (value.length > 20) {
        callback(new Error('密码不能超过20个字符'))
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/.test(value)) {
        callback(new Error('密码必须包含大小写字母和数字'))
      } else {
        callback()
      }
    }

    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请确认密码'))
      } else if (value !== this.registerForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    const validateRealName = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入真实姓名'))
      } else if (value.length < 2) {
        callback(new Error('姓名至少2个字符'))
      } else if (value.length > 10) {
        callback(new Error('姓名不能超过10个字符'))
      } else {
        callback()
      }
    }

    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号码'))
      } else if (!validPhone(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }

    const validateAgreement = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请阅读并同意用户协议和隐私政策'))
      } else {
        callback()
      }
    }

    return {
      registerForm: {
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        realName: '',
        phone: '',
        agreement: false
      },
      registerRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        email: [{ required: true, trigger: 'blur', validator: validateEmail }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }],
        confirmPassword: [{ required: true, trigger: 'blur', validator: validateConfirmPassword }],
        realName: [{ required: true, trigger: 'blur', validator: validateRealName }],
        phone: [{ required: true, trigger: 'blur', validator: validatePhone }],
        agreement: [{ required: true, trigger: 'change', validator: validateAgreement }]
      },
      loading: false,
      passwordType: 'password',
      confirmPasswordType: 'password',
      agreementVisible: false,
      privacyVisible: false
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    showConfirmPwd() {
      if (this.confirmPasswordType === 'password') {
        this.confirmPasswordType = ''
      } else {
        this.confirmPasswordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.confirmPassword.focus()
      })
    },
    async checkUsernameAvailable() {
      if (this.registerForm.username && this.registerForm.username.length >= 3) {
        try {
          await checkUsername(this.registerForm.username)
          // 如果没有抛出异常，说明用户名可用
        } catch (error) {
          this.$message.error('用户名已存在')
        }
      }
    },
    async checkEmailAvailable() {
      if (this.registerForm.email && validEmail(this.registerForm.email)) {
        try {
          await checkEmail(this.registerForm.email)
          // 如果没有抛出异常，说明邮箱可用
        } catch (error) {
          this.$message.error('邮箱已被注册')
        }
      }
    },
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true
          const registerData = {
            username: this.registerForm.username,
            email: this.registerForm.email,
            password: this.registerForm.password,
            realName: this.registerForm.realName,
            phone: this.registerForm.phone
          }
          
          register(registerData).then(response => {
            this.$message.success('注册成功！请登录')
            this.$router.push('/login')
          }).catch(error => {
            this.$message.error(error.message || '注册失败，请重试')
          }).finally(() => {
            this.loading = false
          })
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    showAgreement() {
      this.agreementVisible = true
    },
    showPrivacy() {
      this.privacyVisible = true
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .register-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.register-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }

  .el-checkbox {
    .el-checkbox__label {
      color: $light_gray;
    }
  }
}
</style>

<style lang="scss" scoped>
$bg:#2d3a4b;
$dark_gray:#889aa4;
$light_gray:#eee;

.register-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;

  .register-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 80px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;
    text-align: center;

    .link-type {
      color: #409EFF;
      text-decoration: none;
      margin-left: 5px;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .agreement-content, .privacy-content {
    max-height: 400px;
    overflow-y: auto;
    
    h4 {
      color: #409EFF;
      margin-top: 20px;
      margin-bottom: 10px;
    }
    
    p {
      line-height: 1.6;
      margin-bottom: 15px;
      text-align: justify;
    }
  }
}
</style>
