<template>
  <div class="dashboard-container">
    <!-- 主标题区域 -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="main-title">
          <span class="title-icon">🔗</span>
          区块链跨境交易系统
        </h1>
        <p class="subtitle">基于 Hyperledger Fabric 的安全、透明、高效的跨境支付解决方案</p>
        <div class="feature-tags">
          <span class="tag">🔒 安全可靠</span>
          <span class="tag">🌐 跨境支付</span>
          <span class="tag">⚡ 实时结算</span>
          <span class="tag">📊 透明追溯</span>
        </div>
      </div>
    </div>

    <!-- 功能概览区域 -->
    <div class="features-section">
      <div class="welcome-info">
        <div class="user-welcome">
          <i class="el-icon-user"></i>
          <span>欢迎您，{{ name }}</span>
        </div>
        <div class="system-status">
          <div class="status-item">
            <i class="el-icon-success status-icon online"></i>
            <span>区块链网络：在线</span>
          </div>
          <div class="status-item">
            <i class="el-icon-success status-icon online"></i>
            <span>交易服务：正常</span>
          </div>
        </div>
      </div>

      <div class="quick-actions">
        <h3>快速操作</h3>
        <div class="action-cards">
          <div class="action-card" @click="navigateTo('/sample/account')">
            <i class="el-icon-user"></i>
            <h4>账户管理</h4>
            <p>创建和管理交易账户</p>
          </div>
          <div class="action-card" @click="navigateTo('/sample/transfer')">
            <i class="el-icon-money"></i>
            <h4>转账交易</h4>
            <p>执行跨境转账操作</p>
          </div>
          <div class="action-card" @click="navigateTo('/sample/init-balance')">
            <i class="el-icon-coin"></i>
            <h4>余额管理</h4>
            <p>初始化账户余额</p>
          </div>
          <div class="action-card" @click="navigateTo('/sample/create-fee-account')">
            <i class="el-icon-office-building"></i>
            <h4>手续费公户</h4>
            <p>管理手续费收取账户</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Dashboard',
  computed: {
    ...mapGetters([
      'name'
    ])
  },
  methods: {
    navigateTo(path) {
      this.$router.push(path)
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  min-height: calc(100vh - 84px);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: url('@/assets/images/blockchain-bg.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

// 主标题区域
.hero-section {
  padding: 80px 20px 60px;
  text-align: center;
  position: relative;
  z-index: 2;

  .hero-content {
    max-width: 800px;
    margin: 0 auto;

    .main-title {
      font-size: 3.5rem;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 20px;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      line-height: 1.2;

      .title-icon {
        font-size: 3rem;
        margin-right: 15px;
        display: inline-block;
        animation: pulse 2s infinite;
      }

      @media (max-width: 768px) {
        font-size: 2.5rem;

        .title-icon {
          font-size: 2rem;
        }
      }
    }

    .subtitle {
      font-size: 1.3rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 30px;
      line-height: 1.6;
      text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);

      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }

    .feature-tags {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 15px;

      .tag {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #ffffff;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }
      }
    }
  }
}

// 功能区域
.features-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin: 0 20px 20px;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    margin: 0 10px 10px;
    padding: 20px;
  }
}

// 欢迎信息
.welcome-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .user-welcome {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    color: #333;
    font-weight: 600;

    i {
      font-size: 1.5rem;
      color: #667eea;
      margin-right: 10px;
    }
  }

  .system-status {
    display: flex;
    gap: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;
    }

    .status-item {
      display: flex;
      align-items: center;
      font-size: 0.9rem;
      color: #666;

      .status-icon {
        margin-right: 8px;

        &.online {
          color: #67c23a;
        }
      }
    }
  }
}

// 快速操作
.quick-actions {
  h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 25px;
    text-align: center;
    font-weight: 600;
  }

  .action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .action-card {
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 15px;
      padding: 30px 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border-color: #667eea;
      }

      i {
        font-size: 2.5rem;
        color: #667eea;
        margin-bottom: 15px;
        display: block;
      }

      h4 {
        font-size: 1.2rem;
        color: #333;
        margin-bottom: 10px;
        font-weight: 600;
      }

      p {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.5;
        margin: 0;
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .action-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .action-cards {
    grid-template-columns: 1fr;
  }

  .hero-section {
    padding: 40px 20px 30px;
  }
}
</style>
