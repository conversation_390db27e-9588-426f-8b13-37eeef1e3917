<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>测试账户创建</span>
      </div>

      <el-button type="primary" @click="testCreateSimpleAccount" :loading="loading">创建简单测试账户</el-button>
      <el-button type="success" @click="testCreateFeeAccount" :loading="loading" style="margin-left: 10px;">创建手续费公户</el-button>

      <div v-if="result" style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 4px;">
        <h4>创建结果：</h4>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>

      <div v-if="error" style="margin-top: 20px; padding: 15px; background-color: #fef0f0; border-radius: 4px; color: red;">
        <h4>错误信息：</h4>
        <pre>{{ error }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script>
import { createAccount } from '@/api/account'

export default {
  name: 'TestCreateAccount',
  data() {
    return {
      loading: false,
      result: null,
      error: null
    }
  },
  methods: {
    async testCreateSimpleAccount() {
      try {
        this.loading = true
        this.result = null
        this.error = null

        const simpleAccount = {
          accountId: 'TEST_SIMPLE_' + Date.now(),
          orgId: 'ORG001',
          accountNumber: '1234' + Date.now().toString().slice(-8),
          accountType: 'SAVING',
          currency: ['CNY'],
          status: true,
          otherInfo: {
            fieldOne: '简单测试账户',
            fieldTwo: false,
            fieldThree: 0.0
          }
        }

        console.log('创建简单测试账户:', simpleAccount)

        const response = await createAccount(simpleAccount)
        this.result = response

        console.log('创建简单测试账户响应:', response)

        if (response && response.data && response.data.code === 20000) {
          this.$message.success('简单测试账户创建成功')
        } else {
          this.$message.error('简单测试账户创建失败')
        }

      } catch (error) {
        console.error('创建简单测试账户失败:', error)
        this.error = error.message || error.toString()
        this.$message.error('创建简单测试账户失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },

    async testCreateFeeAccount() {
      try {
        this.loading = true
        this.result = null
        this.error = null

        const feeAccount = {
          accountId: 'FEE_ACCOUNT_SYSTEM',
          orgId: 'ORG001',
          accountNumber: 'SYS' + Date.now().toString().slice(-10),
          accountType: 'SAVING',
          currency: ['CNY', 'USD', 'EUR', 'HKD'],
          status: true,
          otherInfo: {
            fieldOne: '系统手续费收取专户',
            fieldTwo: true,
            fieldThree: 0.0
          }
        }

        console.log('创建手续费公户:', feeAccount)

        const response = await createAccount(feeAccount)
        this.result = response

        console.log('创建手续费公户响应:', response)

        if (response && response.data && response.data.code === 20000) {
          this.$message.success('手续费公户创建成功')
        } else {
          this.$message.error('手续费公户创建失败')
        }

      } catch (error) {
        console.error('创建手续费公户失败:', error)
        this.error = error.message || error.toString()
        this.$message.error('创建手续费公户失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
  font-size: 12px;
}
</style>
