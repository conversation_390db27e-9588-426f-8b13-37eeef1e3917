<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="card">
      <!-- 转账操作 -->
      <el-tab-pane label="转账操作" name="transfer">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>新建转账</span>
          </div>
          <transfer-form
            ref="transferForm"
            :account-list="accountList"
            :transfer="currentTransfer"
            @submit="handleTransfer"
          />
        </el-card>
      </el-tab-pane>

      <!-- 转账记录 -->
      <el-tab-pane label="转账记录" name="records">
        <div class="filter-container">
          <el-select
            v-model="listQuery.accountId"
            placeholder="选择账户"
            clearable
            style="width: 200px"
            class="filter-item"
          >
            <el-option
              v-for="account in accountList"
              :key="account.accountId"
              :label="`${account.accountId} (${account.accountNumber})`"
              :value="account.accountId"
            />
          </el-select>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            搜索
          </el-button>
        </div>

        <el-table
          v-loading="listLoading"
          :data="transferList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="转账ID" prop="transferId" align="center" width="180">
            <template slot-scope="scope">
              <span>{{ scope.row.transferId }}</span>
            </template>
          </el-table-column>
          <el-table-column label="转出账户" prop="fromAccountId" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.fromAccountId }}</span>
            </template>
          </el-table-column>
          <el-table-column label="转入账户" prop="toAccountId" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.toAccountId }}</span>
            </template>
          </el-table-column>
          <el-table-column label="金额" prop="amount" align="center" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.amount }} {{ scope.row.currency }}</span>
            </template>
          </el-table-column>
          <el-table-column label="手续费" prop="fee" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.fee }} {{ scope.row.currency }}</span>
            </template>
          </el-table-column>
          <el-table-column label="转账类型" prop="transferType" align="center" width="120">
            <template slot-scope="scope">
              <el-tag :type="getTransferTypeTag(scope.row.transferType)">
                {{ formatTransferType(scope.row.transferType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" align="center" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusTag(scope.row.status)">
                {{ formatStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createdTimestamp" align="center" width="180">
            <template slot-scope="scope">
              <span>{{ formatTime(scope.row.createdTimestamp) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="说明" prop="description" align="center" min-width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.description }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="info" size="mini" @click="handleDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-container">
          <el-button
            :disabled="!hasNextPage"
            type="primary"
            @click="loadNextPage"
          >
            加载更多
          </el-button>
          <span v-if="transferList.length > 0" class="pagination-info">
            当前已加载 {{ transferList.length }} 条记录
          </span>
          <span v-else class="pagination-info">
            暂无数据
          </span>
        </div>
      </el-tab-pane>

      <!-- 余额查询 -->
      <el-tab-pane label="余额查询" name="balance">
        <div class="filter-container">
          <el-select
            v-model="balanceQuery.accountId"
            placeholder="选择账户"
            style="width: 200px"
            class="filter-item"
          >
            <el-option
              v-for="account in accountList"
              :key="account.accountId"
              :label="`${account.accountId} (${account.accountNumber})`"
              :value="account.accountId"
            />
          </el-select>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="handleBalanceQuery"
          >
            查询余额
          </el-button>
        </div>

        <el-table
          v-loading="balanceLoading"
          :data="balanceList"
          element-loading-text="加载中..."
          border
          fit
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="账户ID" prop="accountId" align="center" width="200">
            <template slot-scope="scope">
              <span>{{ scope.row.accountId }}</span>
            </template>
          </el-table-column>
          <el-table-column label="币种" prop="currency" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.currency }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总余额" prop="balance" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.balance }}</span>
            </template>
          </el-table-column>
          <el-table-column label="冻结金额" prop="frozenAmount" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.frozenAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="可用余额" prop="availableAmount" align="center" width="150">
            <template slot-scope="scope">
              <span style="color: #67C23A; font-weight: bold;">{{ scope.row.availableAmount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最后更新时间" prop="lastUpdatedTimestamp" align="center" width="180">
            <template slot-scope="scope">
              <span>{{ formatTime(scope.row.lastUpdatedTimestamp) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 转账详情对话框 -->
    <el-dialog title="转账详情" :visible.sync="detailVisible" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="转账ID">{{ currentDetail.transferId }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusTag(currentDetail.status)">
            {{ formatStatus(currentDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="转出账户">{{ currentDetail.fromAccountId }}</el-descriptions-item>
        <el-descriptions-item label="转入账户">{{ currentDetail.toAccountId }}</el-descriptions-item>
        <el-descriptions-item label="转账金额">{{ currentDetail.amount }} {{ currentDetail.currency }}</el-descriptions-item>
        <el-descriptions-item label="手续费">{{ currentDetail.fee }} {{ currentDetail.currency }}</el-descriptions-item>
        <el-descriptions-item label="转账类型">{{ formatTransferType(currentDetail.transferType) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(currentDetail.createdTimestamp) }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ formatTime(currentDetail.completedTimestamp) }}</el-descriptions-item>
        <el-descriptions-item label="说明" :span="2">{{ currentDetail.description }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { transfer, queryTransfersByAccount, getAccountBalance } from '@/api/transfer'
import { getAccountPage } from '@/api/account'
import TransferForm from './components/TransferForm'

export default {
  name: 'TransferManagement',
  components: { TransferForm },
  data() {
    return {
      activeTab: 'transfer',
      accountList: [],
      transferList: [],
      balanceList: [],
      listLoading: false,
      balanceLoading: false,
      detailVisible: false,
      hasNextPage: true,
      bookmark: '',
      currentTransfer: {
        transferId: '',
        fromAccountId: '',
        toAccountId: '',
        amount: null,
        currency: '',
        transferType: 'INTERNAL',
        description: '',
        fee: 0,
        otherInfo: {
          remark: ''
        }
      },
      currentDetail: {},
      listQuery: {
        accountId: undefined
      },
      balanceQuery: {
        accountId: undefined
      }
    }
  },
  created() {
    this.loadAccountList()
  },
  methods: {
    async loadAccountList() {
      try {
        const response = await getAccountPage({
          pageSize: 100,
          bookmark: '',
          params: {}
        })
        if (response.data && response.data.data && response.data.data.records) {
          this.accountList = response.data.data.records
        }
      } catch (error) {
        console.error('加载账户列表失败:', error)
      }
    },
    async handleTransfer(transferData) {
      try {
        this.listLoading = true
        const response = await transfer(transferData)
        if (response.data && response.data.code === 20000) {
          this.$message.success('转账成功')
          this.resetTransferForm()
          // 刷新转账记录
          if (this.activeTab === 'records') {
            this.handleFilter()
          }
        }
      } catch (error) {
        this.$message.error('转账失败: ' + (error.message || '未知错误'))
      } finally {
        this.listLoading = false
      }
    },
    resetTransferForm() {
      this.currentTransfer = {
        transferId: '',
        fromAccountId: '',
        toAccountId: '',
        amount: null,
        currency: '',
        transferType: 'INTERNAL',
        description: '',
        fee: 0,
        otherInfo: {
          remark: ''
        }
      }
    },
    async handleFilter() {
      if (!this.listQuery.accountId) {
        this.$message.warning('请选择账户')
        return
      }

      this.transferList = []
      this.bookmark = ''
      this.hasNextPage = true
      await this.fetchTransferData()
    },
    async loadNextPage() {
      if (this.hasNextPage) {
        await this.fetchTransferData()
      }
    },
    async fetchTransferData() {
      try {
        this.listLoading = true
        const response = await queryTransfersByAccount({
          accountId: this.listQuery.accountId,
          pageSize: 10,
          bookmark: this.bookmark
        })

        if (response.data && response.data.data) {
          const newRecords = response.data.data.records || []
          this.transferList = [...this.transferList, ...newRecords]

          const prevBookmark = this.bookmark
          this.bookmark = response.data.data.bookmark

          if (this.bookmark === prevBookmark || newRecords.length === 0) {
            this.hasNextPage = false
          } else {
            this.hasNextPage = true
          }
        }
      } catch (error) {
        console.error('查询转账记录失败:', error)
        this.$message.error('查询转账记录失败')
      } finally {
        this.listLoading = false
      }
    },
    async handleBalanceQuery() {
      if (!this.balanceQuery.accountId) {
        this.$message.warning('请选择账户')
        return
      }

      try {
        this.balanceLoading = true
        this.balanceList = []

        const currencies = ['CNY', 'USD', 'EUR', 'HKD']
        for (const currency of currencies) {
          try {
            const response = await getAccountBalance(this.balanceQuery.accountId, currency)
            if (response.data && response.data.data) {
              this.balanceList.push(response.data.data)
            }
          } catch (error) {
            // 如果某个币种余额不存在，跳过
            console.warn(`账户 ${this.balanceQuery.accountId} 的 ${currency} 余额不存在`)
          }
        }
      } catch (error) {
        console.error('查询余额失败:', error)
        this.$message.error('查询余额失败')
      } finally {
        this.balanceLoading = false
      }
    },
    handleDetail(row) {
      this.currentDetail = row
      this.detailVisible = true
    },
    formatTransferType(type) {
      const typeMap = {
        'INTERNAL': '内部转账',
        'EXTERNAL': '外部转账',
        'CROSS_BORDER': '跨境转账'
      }
      return typeMap[type] || type
    },
    getTransferTypeTag(type) {
      const tagMap = {
        'INTERNAL': 'success',
        'EXTERNAL': 'warning',
        'CROSS_BORDER': 'danger'
      }
      return tagMap[type] || 'info'
    },
    formatStatus(status) {
      const statusMap = {
        'PENDING': '处理中',
        'COMPLETED': '已完成',
        'FAILED': '失败',
        'CANCELLED': '已取消'
      }
      return statusMap[status] || status
    },
    getStatusTag(status) {
      const tagMap = {
        'PENDING': 'warning',
        'COMPLETED': 'success',
        'FAILED': 'danger',
        'CANCELLED': 'info'
      }
      return tagMap[status] || 'info'
    },
    formatTime(timestamp) {
      if (!timestamp) {
        return ''
      }
      const date = new Date(timestamp)
      return date.toLocaleString()
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-item {
  margin-bottom: 10px;
  margin-right: 10px;
}
.pagination-container {
  padding: 20px 0;
  display: flex;
  align-items: center;
}
.pagination-info {
  margin-left: 15px;
  color: #606266;
}
.box-card {
  margin-bottom: 20px;
}
</style>
