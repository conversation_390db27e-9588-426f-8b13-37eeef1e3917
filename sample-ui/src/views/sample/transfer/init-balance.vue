<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>初始化账户余额</span>
        <div style="float: right;">
          <el-button style="padding: 3px 0; margin-right: 10px" type="text" @click="quickInitBalance">快速初始化</el-button>
          <el-button style="padding: 3px 0; margin-right: 10px" type="text" @click="testDirectAPI">直接API</el-button>
          <el-button style="padding: 3px 0" type="text" @click="testSingleBalance">测试查询</el-button>
        </div>
      </div>

      <el-form ref="balanceForm" :model="balanceForm" :rules="rules" label-width="120px" style="max-width: 500px;">
        <el-form-item label="账户" prop="accountId">
          <el-select v-model="balanceForm.accountId" placeholder="请选择账户" filterable style="width: 100%;">
            <el-option
              v-for="account in accountList"
              :key="account.accountId"
              :label="`${account.accountId} (${account.accountNumber})`"
              :value="account.accountId"
            />
          </el-select>
          <div v-if="accountList.length === 0" style="color: #909399; font-size: 12px; margin-top: 5px;">
            没有可用账户，请先在“账户管理”中创建账户
          </div>
        </el-form-item>

        <el-form-item label="币种" prop="currency">
          <el-select v-model="balanceForm.currency" placeholder="请选择币种" style="width: 100%;">
            <el-option label="人民币" value="CNY" />
            <el-option label="美元" value="USD" />
            <el-option label="欧元" value="EUR" />
            <el-option label="港币" value="HKD" />
          </el-select>
        </el-form-item>

        <el-form-item label="初始余额" prop="initialBalance">
          <el-input-number
            v-model="balanceForm.initialBalance"
            :precision="2"
            :min="0"
            placeholder="请输入初始余额"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">初始化</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>


    </el-card>

    <!-- 已初始化的余额列表 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>已初始化的账户余额</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="loadBalanceList">刷新</el-button>
      </div>

      <el-table
        v-loading="tableLoading"
        :data="balanceList"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="账户ID" prop="accountId" align="center" width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.accountId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="币种" prop="currency" align="center" width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.currency }}</span>
          </template>
        </el-table-column>
        <el-table-column label="余额" prop="balance" align="center" width="150">
          <template slot-scope="scope">
            <span style="color: #67C23A; font-weight: bold;">{{ scope.row.balance }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用余额" prop="availableAmount" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.availableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="冻结金额" prop="frozenAmount" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.frozenAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最后更新时间" prop="lastUpdatedTimestamp" align="center" width="180">
          <template slot-scope="scope">
            <span>{{ formatTime(scope.row.lastUpdatedTimestamp) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { initAccountBalance, getAccountBalance } from '@/api/transfer'
import { getAccountPage } from '@/api/account'

export default {
  name: 'InitBalance',
  data() {
    return {
      loading: false,
      tableLoading: false,
      accountList: [],
      balanceList: [],
      balanceForm: {
        accountId: '',
        currency: '',
        initialBalance: null
      },
      rules: {
        accountId: [
          { required: true, message: '请选择账户', trigger: 'change' }
        ],
        currency: [
          { required: true, message: '请选择币种', trigger: 'change' }
        ],
        initialBalance: [
          { required: true, message: '请输入初始余额', trigger: 'blur' },
          { type: 'number', min: 0, message: '初始余额不能为负数', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadAccountList()
  },
  methods: {
    async loadAccountList() {
      try {
        const response = await getAccountPage({
          pageSize: 100,
          bookmark: '',
          params: {}
        })

        // 检查多种可能的数据结构
        if (response && response.data) {
          if (response.data.data && response.data.data.records) {
            this.accountList = response.data.data.records
          } else if (response.data.records) {
            this.accountList = response.data.records
          } else if (Array.isArray(response.data)) {
            this.accountList = response.data
          } else {
            this.accountList = []
          }
        } else {
          this.accountList = []
        }
      } catch (error) {
        console.error('加载账户列表失败:', error)
        this.$message.error('加载账户列表失败: ' + (error.message || '未知错误'))
        this.accountList = []
      }
    },
    async handleSubmit() {
      this.$refs.balanceForm.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true
            const response = await initAccountBalance({
              accountId: this.balanceForm.accountId,
              currency: this.balanceForm.currency,
              initialBalance: this.balanceForm.initialBalance.toString()
            })

            if (response.data && response.data.code === 20000) {
              this.$message.success('账户余额初始化成功')
              this.resetForm()
              this.loadBalanceList()
            }
          } catch (error) {
            this.$message.error('初始化失败: ' + (error.message || '未知错误'))
          } finally {
            this.loading = false
          }
        }
      })
    },
    async loadBalanceList() {
      try {
        this.tableLoading = true
        this.balanceList = []

        // 先获取所有已初始化的余额键
        let availableBalances = []
        try {
          // 使用 fetch 调用调试接口
          const response = await fetch('/api/transfer/debug/balance-keys')
          console.log('调试接口 HTTP 状态:', response.status)

          if (response.ok) {
            const debugResponse = await response.json()
            console.log('调试接口响应:', debugResponse)

            if (debugResponse && debugResponse.data && debugResponse.data.balanceKeys) {
              availableBalances = debugResponse.data.balanceKeys
              console.log('已初始化的余额键:', availableBalances)
            } else {
              console.log('调试接口返回数据格式不正确')
            }
          } else {
            console.error('调试接口调用失败:', response.status)
          }
        } catch (error) {
          console.warn('获取余额键失败，使用默认查询方式:', error)
        }

        if (availableBalances.length > 0) {
          // 根据已初始化的余额键查询
          for (const balanceKey of availableBalances) {
            // 解析余额键：BLANCE_ACC0001_USD -> accountId: ACC0001, currency: USD
            const match = balanceKey.match(/^BALANCE_(.+)_([A-Z]{3})$/)
            if (match) {
              const accountId = match[1]
              const currency = match[2]

              try {
                console.log(`查询余额: ${accountId} - ${currency}`)
                const response = await getAccountBalance(accountId, currency)
                console.log(`余额查询响应 ${accountId}-${currency}:`, response)

                // axios 响应拦截器直接返回 ResultUtil 对象：{ code, message, data, timestamp }
                if (response && response.data) {
                  console.log(`添加余额数据: ${accountId}-${currency}`, response.data)
                  this.balanceList.push(response.data)
                } else {
                  console.warn(`余额数据不存在: ${accountId}-${currency}`, response || 'no response')
                }
              } catch (error) {
                console.warn(`查询余额失败: ${accountId} - ${currency}`, error)
              }
            }
          }
        } else {
          // 如果没有找到已初始化的余额，使用原来的方式
          const currencies = ['CNY', 'USD', 'EUR', 'HKD']
          for (const account of this.accountList) {
            for (const currency of currencies) {
              try {
                const response = await getAccountBalance(account.accountId, currency)
                // axios 响应拦截器直接返回 ResultUtil 对象：{ code, message, data, timestamp }
                if (response && response.data) {
                  this.balanceList.push(response.data)
                }
              } catch (error) {
                // 如果某个账户的某个币种余额不存在，跳过
                console.warn(`账户 ${account.accountId} 的 ${currency} 余额不存在`)
              }
            }
          }
        }

        console.log('最终余额列表:', this.balanceList)
        console.log('余额列表数量:', this.balanceList.length)

        if (this.balanceList.length === 0) {
          this.$message.info('暂无已初始化的账户余额，请先初始化余额')
        } else {
          console.log(`成功加载 ${this.balanceList.length} 条余额记录`)
        }

      } catch (error) {
        console.error('加载余额列表失败:', error)
        this.$message.error('加载余额列表失败')
      } finally {
        this.tableLoading = false
      }
    },
    resetForm() {
      this.$refs.balanceForm.resetFields()
      this.balanceForm = {
        accountId: '',
        currency: '',
        initialBalance: null
      }
    },
    // 测试方法：直接查询单个余额
    async testSingleBalance() {
      try {
        console.log('测试查询 ACC0001 CNY 余额')

        // 同时测试 axios 和 fetch 调用
        console.log('=== 测试 axios 调用 ===')
        const axiosResponse = await getAccountBalance('ACC0001', 'CNY')
        console.log('axios 响应 - 完整:', JSON.stringify(axiosResponse, null, 2))
        console.log('axios 响应 - response:', axiosResponse)
        console.log('axios 响应 - response.data:', axiosResponse.data)

        console.log('=== 测试 fetch 调用 ===')
        const fetchResponse = await fetch('http://localhost:8080/transfer/balance/ACC0001/CNY')
        const fetchData = await fetchResponse.json()
        console.log('fetch 响应 - 完整:', JSON.stringify(fetchData, null, 2))
        console.log('fetch 响应 - data:', fetchData)

        // 比较 axios 和 fetch 的响应
        console.log('=== 比较结果 ===')
        console.log('axios 响应类型:', typeof axiosResponse)
        console.log('fetch 响应类型:', typeof fetchData)

        // 检查 axios 响应
        // axios 响应拦截器直接返回 response.data，所以 axiosResponse 就是 ResultUtil 对象
        if (axiosResponse) {
          console.log('axios - 响应存在')
          console.log('axios - 响应的所有键:', Object.keys(axiosResponse))
          console.log('axios - code:', axiosResponse.code)
          console.log('axios - message:', axiosResponse.message)
          console.log('axios - data:', axiosResponse.data)

          if (axiosResponse.data) {
            console.log('axios - 成功获取余额数据:', axiosResponse.data)
            this.$message.success('测试查询成功 (axios)')
          } else {
            console.log('axios - 响应中没有余额数据')
            this.$message.warning(`axios 查询无余额数据 - ${axiosResponse.message || '未知错误'}`)
          }
        } else {
          console.log('axios - 响应不存在')
          this.$message.warning('axios 响应结构异常')
        }

        // 检查 fetch 响应
        if (fetchData && fetchData.data) {
          console.log('fetch - 成功获取余额数据:', fetchData.data)
          this.$message.success('测试查询成功 (fetch)')
        } else {
          console.log('fetch - 响应中没有余额数据')
          this.$message.warning(`fetch 查询无余额数据 - ${fetchData.message || '未知错误'}`)
        }
      } catch (error) {
        console.error('测试查询失败:', error)
        this.$message.error('测试查询失败: ' + (error.message || '未知错误'))
      }
    },
    // 快速初始化测试账户余额
    async quickInitBalance() {
      try {
        this.loading = true

        const accounts = [
          { accountId: 'ACC0001', currencies: ['CNY', 'USD', 'EUR', 'HKD'], balance: '1000.00' },
          { accountId: 'FEE_ACCOUNT_SYSTEM', currencies: ['CNY', 'USD', 'EUR', 'HKD'], balance: '0.00' }
        ]

        let successCount = 0
        let totalCount = 0

        for (const account of accounts) {
          for (const currency of account.currencies) {
            totalCount++
            try {
              console.log(`初始化 ${account.accountId} - ${currency}`)
              const response = await initAccountBalance({
                accountId: account.accountId,
                currency: currency,
                initialBalance: account.balance
              })

              if (response && response.data && response.data.code === 20000) {
                successCount++
                console.log(`初始化成功: ${account.accountId} - ${currency}`)
              } else {
                console.warn(`初始化失败: ${account.accountId} - ${currency}`, response)
              }
            } catch (error) {
              console.error(`初始化错误: ${account.accountId} - ${currency}`, error)
            }

            // 添加小延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 200))
          }
        }

        this.$message.success(`快速初始化完成: ${successCount}/${totalCount} 成功`)

        // 重新加载余额列表
        setTimeout(() => {
          this.loadBalanceList()
        }, 1000)

      } catch (error) {
        console.error('快速初始化失败:', error)
        this.$message.error('快速初始化失败: ' + (error.message || '未知错误'))
      } finally {
        this.loading = false
      }
    },
    // 直接测试后端 API
    async testDirectAPI() {
      try {
        console.log('直接测试后端 API')

        // 尝试不同的 API 路径
        const paths = [
          '/api/transfer/balance/ACC0001/CNY',
          '/transfer/balance/ACC0001/CNY',
          'http://localhost:8080/transfer/balance/ACC0001/CNY'
        ]

        for (const path of paths) {
          try {
            console.log(`尝试路径: ${path}`)
            const response = await fetch(path)
            console.log(`路径 ${path} - HTTP 状态码:`, response.status)
            console.log(`路径 ${path} - Content-Type:`, response.headers.get('content-type'))

            if (response.ok) {
              const contentType = response.headers.get('content-type')
              if (contentType && contentType.includes('application/json')) {
                const data = await response.json()
                console.log(`路径 ${path} - JSON 响应:`, data)
                this.$message.success(`直接 API 调用成功: ${path}`)
                return
              } else {
                const text = await response.text()
                console.log(`路径 ${path} - 文本响应 (前100字符):`, text.substring(0, 100))
              }
            }
          } catch (error) {
            console.log(`路径 ${path} - 错误:`, error.message)
          }
        }

        this.$message.error('所有 API 路径都失败')
      } catch (error) {
        console.error('直接 API 测试失败:', error)
        this.$message.error('直接 API 测试失败: ' + error.message)
      }
    },
    formatTime(timestamp) {
      if (!timestamp) {
        return ''
      }
      const date = new Date(timestamp)
      return date.toLocaleString()
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
