<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>初始化账户余额</span>
      </div>

      <el-form ref="balanceForm" :model="balanceForm" :rules="rules" label-width="120px" style="max-width: 500px;">
        <el-form-item label="账户" prop="accountId">
          <el-select v-model="balanceForm.accountId" placeholder="请选择账户" filterable style="width: 100%;">
            <el-option
              v-for="account in accountList"
              :key="account.accountId"
              :label="`${account.accountId} (${account.accountNumber})`"
              :value="account.accountId"
            />
          </el-select>
          <div v-if="accountList.length === 0" style="color: #909399; font-size: 12px; margin-top: 5px;">
            没有可用账户，请先在“账户管理”中创建账户
          </div>
        </el-form-item>

        <el-form-item label="币种" prop="currency">
          <el-select v-model="balanceForm.currency" placeholder="请选择币种" style="width: 100%;">
            <el-option label="人民币" value="CNY" />
            <el-option label="美元" value="USD" />
            <el-option label="欧元" value="EUR" />
            <el-option label="港币" value="HKD" />
          </el-select>
        </el-form-item>

        <el-form-item label="初始余额" prop="initialBalance">
          <el-input-number
            v-model="balanceForm.initialBalance"
            :precision="2"
            :min="0"
            placeholder="请输入初始余额"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">初始化</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>


    </el-card>

    <!-- 已初始化的余额列表 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>已初始化的账户余额</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="loadBalanceList">刷新</el-button>
      </div>

      <el-table
        v-loading="tableLoading"
        :data="balanceList"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="账户ID" prop="accountId" align="center" width="200">
          <template slot-scope="scope">
            <span>{{ scope.row.accountId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="币种" prop="currency" align="center" width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.currency }}</span>
          </template>
        </el-table-column>
        <el-table-column label="余额" prop="balance" align="center" width="150">
          <template slot-scope="scope">
            <span style="color: #67C23A; font-weight: bold;">{{ scope.row.balance }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用余额" prop="availableAmount" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.availableAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="冻结金额" prop="frozenAmount" align="center" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.frozenAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最后更新时间" prop="lastUpdatedTimestamp" align="center" width="180">
          <template slot-scope="scope">
            <span>{{ formatTime(scope.row.lastUpdatedTimestamp) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { initAccountBalance, getAccountBalance } from '@/api/transfer'
import { getAccountPage } from '@/api/account'

export default {
  name: 'InitBalance',
  data() {
    return {
      loading: false,
      tableLoading: false,
      accountList: [],
      balanceList: [],
      balanceForm: {
        accountId: '',
        currency: '',
        initialBalance: null
      },
      rules: {
        accountId: [
          { required: true, message: '请选择账户', trigger: 'change' }
        ],
        currency: [
          { required: true, message: '请选择币种', trigger: 'change' }
        ],
        initialBalance: [
          { required: true, message: '请输入初始余额', trigger: 'blur' },
          { type: 'number', min: 0, message: '初始余额不能为负数', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.loadAccountList()
  },
  methods: {
    async loadAccountList() {
      try {
        const response = await getAccountPage({
          pageSize: 100,
          bookmark: '',
          params: {}
        })

        // 检查多种可能的数据结构
        if (response && response.data) {
          if (response.data.data && response.data.data.records) {
            this.accountList = response.data.data.records
          } else if (response.data.records) {
            this.accountList = response.data.records
          } else if (Array.isArray(response.data)) {
            this.accountList = response.data
          } else {
            this.accountList = []
          }
        } else {
          this.accountList = []
        }
      } catch (error) {
        console.error('加载账户列表失败:', error)
        this.$message.error('加载账户列表失败')
        this.accountList = []
      }
    },
    async handleSubmit() {
      this.$refs.balanceForm.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true
            const response = await initAccountBalance({
              accountId: this.balanceForm.accountId,
              currency: this.balanceForm.currency,
              initialBalance: this.balanceForm.initialBalance.toString()
            })

            if (response.data && response.data.code === 20000) {
              this.$message.success('账户余额初始化成功')
              this.resetForm()
              this.loadBalanceList()
            }
          } catch (error) {
            this.$message.error('初始化失败: ' + (error.message || '未知错误'))
          } finally {
            this.loading = false
          }
        }
      })
    },
    async loadBalanceList() {
      try {
        this.tableLoading = true
        this.balanceList = []

        // 先获取所有已初始化的余额键
        let availableBalances = []
        try {
          const response = await fetch('/api/transfer/debug/balance-keys')
          if (response.ok) {
            const debugResponse = await response.json()
            if (debugResponse && debugResponse.data && debugResponse.data.balanceKeys) {
              availableBalances = debugResponse.data.balanceKeys
            }
          }
        } catch (error) {
          // 如果调试接口失败，使用默认查询方式
        }

        if (availableBalances.length > 0) {
          // 根据已初始化的余额键查询
          for (const balanceKey of availableBalances) {
            // 解析余额键：BLANCE_ACC0001_USD -> accountId: ACC0001, currency: USD
            const match = balanceKey.match(/^BALANCE_(.+)_([A-Z]{3})$/)
            if (match) {
              const accountId = match[1]
              const currency = match[2]

              try {
                const response = await getAccountBalance(accountId, currency)
                // axios 响应拦截器直接返回 ResultUtil 对象：{ code, message, data, timestamp }
                if (response && response.data) {
                  this.balanceList.push(response.data)
                }
              } catch (error) {
                // 如果某个余额不存在，跳过
              }
            }
          }
        } else {
          // 如果没有找到已初始化的余额，使用原来的方式
          const currencies = ['CNY', 'USD', 'EUR', 'HKD']
          for (const account of this.accountList) {
            for (const currency of currencies) {
              try {
                const response = await getAccountBalance(account.accountId, currency)
                // axios 响应拦截器直接返回 ResultUtil 对象：{ code, message, data, timestamp }
                if (response && response.data) {
                  this.balanceList.push(response.data)
                }
              } catch (error) {
                // 如果某个账户的某个币种余额不存在，跳过
              }
            }
          }
        }

        if (this.balanceList.length === 0) {
          this.$message.info('暂无已初始化的账户余额，请先初始化余额')
        }

      } catch (error) {
        console.error('加载余额列表失败:', error)
        this.$message.error('加载余额列表失败')
      } finally {
        this.tableLoading = false
      }
    },
    resetForm() {
      this.$refs.balanceForm.resetFields()
      this.balanceForm = {
        accountId: '',
        currency: '',
        initialBalance: null
      }
    },
    formatTime(timestamp) {
      if (!timestamp) {
        return ''
      }
      const date = new Date(timestamp)
      return date.toLocaleString()
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
