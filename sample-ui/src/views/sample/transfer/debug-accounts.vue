<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>账户API调试</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="testAccountAPI">测试API</el-button>
      </div>
      
      <div>
        <h4>API响应原始数据：</h4>
        <pre>{{ JSON.stringify(apiResponse, null, 2) }}</pre>
        
        <h4>解析后的账户列表：</h4>
        <pre>{{ JSON.stringify(accountList, null, 2) }}</pre>
        
        <h4>账户数量：{{ accountList.length }}</h4>
        
        <div v-if="accountList.length === 0" style="color: red; margin-top: 20px;">
          <p><strong>没有找到账户数据！</strong></p>
          <p>可能的原因：</p>
          <ul>
            <li>后端服务未启动</li>
            <li>没有创建任何账户</li>
            <li>API路径错误</li>
            <li>数据格式不匹配</li>
          </ul>
        </div>
        
        <div v-if="error" style="color: red; margin-top: 20px;">
          <h4>错误信息：</h4>
          <pre>{{ error }}</pre>
        </div>
      </div>
    </el-card>
    
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>快速创建测试账户</span>
      </div>
      
      <el-button type="primary" @click="createTestAccount" :loading="creating">创建测试账户</el-button>
      <p>将创建一个测试账户用于转账功能测试</p>
      
      <div v-if="createResult" style="margin-top: 20px;">
        <h4>创建结果：</h4>
        <pre>{{ JSON.stringify(createResult, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getAccountPage, createAccount } from '@/api/account'

export default {
  name: 'DebugAccounts',
  data() {
    return {
      apiResponse: null,
      accountList: [],
      error: null,
      creating: false,
      createResult: null
    }
  },
  mounted() {
    this.testAccountAPI()
  },
  methods: {
    async testAccountAPI() {
      try {
        this.error = null
        console.log('开始测试账户API...')
        
        const response = await getAccountPage({
          pageSize: 100,
          bookmark: '',
          params: {}
        })
        
        this.apiResponse = response
        console.log('API原始响应:', response)
        
        // 尝试解析数据
        if (response && response.data) {
          if (response.data.data && response.data.data.records) {
            this.accountList = response.data.data.records
          } else if (response.data.records) {
            this.accountList = response.data.records
          } else if (Array.isArray(response.data)) {
            this.accountList = response.data
          } else {
            this.accountList = []
          }
        } else {
          this.accountList = []
        }
        
        console.log('解析后的账户列表:', this.accountList)
        
      } catch (error) {
        console.error('测试账户API失败:', error)
        this.error = error.message || error.toString()
        this.accountList = []
      }
    },
    
    async createTestAccount() {
      try {
        this.creating = true
        this.createResult = null
        
        const testAccount = {
          accountId: 'TEST_ACC_' + Date.now(),
          orgId: 'ORG001',
          accountNumber: '6222' + Date.now().toString().slice(-12),
          accountType: 'SAVING',
          currency: ['CNY', 'USD'],
          status: true,
          otherInfo: {
            remark: '测试账户，用于转账功能测试'
          }
        }
        
        console.log('创建测试账户:', testAccount)
        
        const response = await createAccount(testAccount)
        this.createResult = response
        
        console.log('创建账户响应:', response)
        
        if (response && response.data && response.data.code === 20000) {
          this.$message.success('测试账户创建成功')
          // 重新加载账户列表
          setTimeout(() => {
            this.testAccountAPI()
          }, 1000)
        } else {
          this.$message.error('创建账户失败')
        }
        
      } catch (error) {
        console.error('创建测试账户失败:', error)
        this.$message.error('创建测试账户失败: ' + (error.message || '未知错误'))
        this.createResult = { error: error.message || error.toString() }
      } finally {
        this.creating = false
      }
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
  font-size: 12px;
}

ul {
  text-align: left;
}
</style>
