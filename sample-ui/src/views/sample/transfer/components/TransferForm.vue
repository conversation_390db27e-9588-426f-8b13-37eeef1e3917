<template>
  <el-form ref="transferForm" :model="transferForm" :rules="rules" label-width="120px">
    <el-form-item label="转账ID" prop="transferId">
      <el-input v-model="transferForm.transferId" placeholder="请输入转账ID" />
    </el-form-item>
    
    <el-form-item label="转出账户" prop="fromAccountId">
      <el-select v-model="transferForm.fromAccountId" placeholder="请选择转出账户" filterable @change="onFromAccountChange">
        <el-option
          v-for="account in accountList"
          :key="account.accountId"
          :label="`${account.accountId} (${account.accountNumber})`"
          :value="account.accountId"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="转入账户" prop="toAccountId">
      <el-select v-model="transferForm.toAccountId" placeholder="请选择转入账户" filterable>
        <el-option
          v-for="account in accountList"
          :key="account.accountId"
          :label="`${account.accountId} (${account.accountNumber})`"
          :value="account.accountId"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="币种" prop="currency">
      <el-select v-model="transferForm.currency" placeholder="请选择币种" @change="onCurrencyChange">
        <el-option label="人民币" value="CNY" />
        <el-option label="美元" value="USD" />
        <el-option label="欧元" value="EUR" />
        <el-option label="港币" value="HKD" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="可用余额" v-if="availableBalance !== null">
      <el-tag type="info">{{ availableBalance }} {{ transferForm.currency }}</el-tag>
    </el-form-item>
    
    <el-form-item label="转账金额" prop="amount">
      <el-input-number
        v-model="transferForm.amount"
        :precision="2"
        :min="0.01"
        :max="availableBalance || *********"
        placeholder="请输入转账金额"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item label="手续费" prop="fee">
      <el-input-number
        v-model="transferForm.fee"
        :precision="2"
        :min="0"
        placeholder="请输入手续费"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item label="转账类型" prop="transferType">
      <el-select v-model="transferForm.transferType" placeholder="请选择转账类型">
        <el-option label="内部转账" value="INTERNAL" />
        <el-option label="外部转账" value="EXTERNAL" />
        <el-option label="跨境转账" value="CROSS_BORDER" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="转账说明" prop="description">
      <el-input
        v-model="transferForm.description"
        type="textarea"
        :rows="3"
        placeholder="请输入转账说明"
      />
    </el-form-item>
    
    <el-form-item label="其他信息">
      <el-form-item label="备注" prop="otherInfo.remark">
        <el-input v-model="transferForm.otherInfo.remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">确认转账</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { getAccountBalance } from '@/api/transfer'

export default {
  name: 'TransferForm',
  props: {
    accountList: {
      type: Array,
      default: () => []
    },
    transfer: {
      type: Object,
      default: () => ({
        transferId: '',
        fromAccountId: '',
        toAccountId: '',
        amount: null,
        currency: '',
        transferType: 'INTERNAL',
        description: '',
        fee: 0,
        otherInfo: {
          remark: ''
        }
      })
    }
  },
  data() {
    return {
      transferForm: {
        transferId: '',
        fromAccountId: '',
        toAccountId: '',
        amount: null,
        currency: '',
        transferType: 'INTERNAL',
        description: '',
        fee: 0,
        otherInfo: {
          remark: ''
        }
      },
      availableBalance: null,
      rules: {
        transferId: [
          { required: true, message: '请输入转账ID', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        fromAccountId: [
          { required: true, message: '请选择转出账户', trigger: 'change' }
        ],
        toAccountId: [
          { required: true, message: '请选择转入账户', trigger: 'change' }
        ],
        currency: [
          { required: true, message: '请选择币种', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入转账金额', trigger: 'blur' },
          { type: 'number', min: 0.01, message: '转账金额必须大于0', trigger: 'blur' }
        ],
        transferType: [
          { required: true, message: '请选择转账类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    transfer: {
      handler(val) {
        this.transferForm = JSON.parse(JSON.stringify(val))
        if (!this.transferForm.otherInfo) {
          this.transferForm.otherInfo = { remark: '' }
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async onFromAccountChange() {
      if (this.transferForm.fromAccountId && this.transferForm.currency) {
        await this.loadBalance()
      }
    },
    async onCurrencyChange() {
      if (this.transferForm.fromAccountId && this.transferForm.currency) {
        await this.loadBalance()
      }
    },
    async loadBalance() {
      try {
        const response = await getAccountBalance(this.transferForm.fromAccountId, this.transferForm.currency)
        if (response.data && response.data.data) {
          this.availableBalance = response.data.data.availableAmount
        }
      } catch (error) {
        console.warn('获取余额失败:', error)
        this.availableBalance = null
      }
    },
    submitForm() {
      this.$refs.transferForm.validate(valid => {
        if (valid) {
          // 验证转出账户和转入账户不能相同
          if (this.transferForm.fromAccountId === this.transferForm.toAccountId) {
            this.$message.error('转出账户和转入账户不能相同')
            return
          }
          
          // 验证余额是否足够
          if (this.availableBalance !== null) {
            const totalAmount = this.transferForm.amount + this.transferForm.fee
            if (totalAmount > this.availableBalance) {
              this.$message.error('余额不足')
              return
            }
          }
          
          // 生成转账ID（如果为空）
          if (!this.transferForm.transferId) {
            this.transferForm.transferId = 'TXN' + Date.now()
          }
          
          this.$emit('submit', this.transferForm)
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    resetForm() {
      this.$refs.transferForm.resetFields()
      this.availableBalance = null
    }
  }
}
</script>
