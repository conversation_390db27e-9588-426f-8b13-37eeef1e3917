<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>创建手续费公户</span>
      </div>
      
      <div style="margin-bottom: 20px;">
        <p>手续费公户是系统专用账户，用于收取所有转账产生的手续费。</p>
        <p><strong>建议账户信息：</strong></p>
        <ul>
          <li>账户ID: FEE_ACCOUNT_SYSTEM</li>
          <li>账户类型: SAVING（储蓄账户）</li>
          <li>支持币种: CNY, USD, EUR, HKD</li>
          <li>用途: 系统手续费收取专户</li>
        </ul>
      </div>
      
      <el-form ref="feeAccountForm" :model="feeAccountForm" :rules="rules" label-width="120px" style="max-width: 500px;">
        <el-form-item label="账户ID" prop="accountId">
          <el-input v-model="feeAccountForm.accountId" placeholder="建议使用: FEE_ACCOUNT_SYSTEM" />
        </el-form-item>
        
        <el-form-item label="组织ID" prop="orgId">
          <el-input v-model="feeAccountForm.orgId" placeholder="请输入组织ID" />
        </el-form-item>
        
        <el-form-item label="账户号码" prop="accountNumber">
          <el-input v-model="feeAccountForm.accountNumber" placeholder="系统自动生成或手动输入" />
        </el-form-item>
        
        <el-form-item label="账户类型" prop="accountType">
          <el-select v-model="feeAccountForm.accountType" placeholder="请选择账户类型" style="width: 100%;">
            <el-option label="储蓄账户" value="SAVING" />
            <el-option label="活期账户" value="CURRENT" />
            <el-option label="外汇账户" value="FEX" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="支持币种" prop="currency">
          <el-checkbox-group v-model="feeAccountForm.currency">
            <el-checkbox label="CNY">人民币</el-checkbox>
            <el-checkbox label="USD">美元</el-checkbox>
            <el-checkbox label="EUR">欧元</el-checkbox>
            <el-checkbox label="HKD">港币</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="feeAccountForm.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="createFeeAccount" :loading="loading">创建手续费公户</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="createResult" style="margin-top: 20px; padding: 15px; background-color: #f0f9ff; border-radius: 4px;">
        <h4>创建结果：</h4>
        <pre>{{ JSON.stringify(createResult, null, 2) }}</pre>
      </div>
    </el-card>
    
    <!-- 初始化手续费账户余额 -->
    <el-card class="box-card" style="margin-top: 20px;" v-if="feeAccountCreated">
      <div slot="header" class="clearfix">
        <span>初始化手续费账户余额</span>
      </div>
      
      <p>为手续费账户初始化各币种余额（建议初始余额为0）：</p>
      
      <div v-for="currency in ['CNY', 'USD', 'EUR', 'HKD']" :key="currency" style="margin-bottom: 10px;">
        <el-button 
          size="small" 
          type="primary" 
          @click="initBalance(currency)"
          :loading="initLoading[currency]"
        >
          初始化 {{ currency }} 余额
        </el-button>
        <span style="margin-left: 10px; color: #909399;">初始余额: 0.00</span>
      </div>
    </el-card>
  </div>
</template>

<script>
import { createAccount } from '@/api/account'
import { initAccountBalance } from '@/api/transfer'

export default {
  name: 'CreateFeeAccount',
  data() {
    return {
      loading: false,
      feeAccountCreated: false,
      createResult: null,
      initLoading: {
        CNY: false,
        USD: false,
        EUR: false,
        HKD: false
      },
      feeAccountForm: {
        accountId: 'FEE_ACCOUNT_SYSTEM',
        orgId: 'ORG001',
        accountNumber: '',
        accountType: 'SAVING',
        currency: ['CNY', 'USD', 'EUR', 'HKD'],
        remark: '系统手续费收取专户，用于收取所有转账产生的手续费'
      },
      rules: {
        accountId: [
          { required: true, message: '请输入账户ID', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        orgId: [
          { required: true, message: '请输入组织ID', trigger: 'blur' }
        ],
        accountType: [
          { required: true, message: '请选择账户类型', trigger: 'change' }
        ],
        currency: [
          { type: 'array', required: true, message: '请至少选择一种币种', trigger: 'change' }
        ]
      }
    }
  },
  mounted() {
    // 自动生成账户号码
    this.feeAccountForm.accountNumber = 'SYS' + Date.now().toString().slice(-10)
  },
  methods: {
    async createFeeAccount() {
      this.$refs.feeAccountForm.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true
            
            const accountData = {
              accountId: this.feeAccountForm.accountId,
              orgId: this.feeAccountForm.orgId,
              accountNumber: this.feeAccountForm.accountNumber,
              accountType: this.feeAccountForm.accountType,
              currency: this.feeAccountForm.currency,
              status: true,
              otherInfo: {
                remark: this.feeAccountForm.remark,
                accountPurpose: 'FEE_COLLECTION', // 标记为手续费收取账户
                isSystemAccount: true // 标记为系统账户
              }
            }
            
            console.log('创建手续费公户:', accountData)
            
            const response = await createAccount(accountData)
            this.createResult = response
            
            console.log('创建手续费公户响应:', response)
            
            if (response && response.data && response.data.code === 20000) {
              this.$message.success('手续费公户创建成功！')
              this.feeAccountCreated = true
              
              // 显示下一步提示
              this.$notify({
                title: '创建成功',
                message: '手续费公户已创建，请继续初始化各币种余额',
                type: 'success',
                duration: 5000
              })
            } else {
              this.$message.error('创建手续费公户失败')
            }
            
          } catch (error) {
            console.error('创建手续费公户失败:', error)
            this.$message.error('创建手续费公户失败: ' + (error.message || '未知错误'))
            this.createResult = { error: error.message || error.toString() }
          } finally {
            this.loading = false
          }
        }
      })
    },
    
    async initBalance(currency) {
      try {
        this.initLoading[currency] = true
        
        const response = await initAccountBalance({
          accountId: this.feeAccountForm.accountId,
          currency: currency,
          initialBalance: '0.00'
        })
        
        console.log(`初始化 ${currency} 余额响应:`, response)
        
        if (response && response.data && response.data.code === 20000) {
          this.$message.success(`${currency} 余额初始化成功`)
        } else {
          this.$message.error(`${currency} 余额初始化失败`)
        }
        
      } catch (error) {
        console.error(`初始化 ${currency} 余额失败:`, error)
        this.$message.error(`初始化 ${currency} 余额失败: ` + (error.message || '未知错误'))
      } finally {
        this.initLoading[currency] = false
      }
    },
    
    resetForm() {
      this.$refs.feeAccountForm.resetFields()
      this.createResult = null
      this.feeAccountCreated = false
      // 重新生成账户号码
      this.feeAccountForm.accountNumber = 'SYS' + Date.now().toString().slice(-10)
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
  font-size: 12px;
}

ul {
  text-align: left;
  padding-left: 20px;
}

li {
  margin-bottom: 5px;
}
</style>
