<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>账户详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      <div>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions class="margin-top" :column="2" border>
              <el-descriptions-item label="账户ID">{{ account.accountId }}</el-descriptions-item>
              <el-descriptions-item label="组织ID">{{ account.orgId }}</el-descriptions-item>
              <el-descriptions-item label="账户号码">{{ account.accountNumber }}</el-descriptions-item>
              <el-descriptions-item label="账户类型">{{ formatAccountType(account.accountType) }}</el-descriptions-item>
              <el-descriptions-item label="币种">
                <el-tag v-for="(item, index) in account.currency" :key="index" style="margin-right: 5px">
                  {{ item }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="account.status ? 'success' : 'danger'">
                  {{ account.status ? '正常' : '已删除' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ formatTime(account.createdTimestamp) }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="JSON数据" name="json">
            <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow: auto; max-height: 400px;">{{ JSON.stringify(account, null, 2) }}</pre>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>账户历史记录</span>
      </div>
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in historyList"
          :key="index"
          :timestamp="formatTime(activity.timestamp)"
          :type="activity.isDelete ? 'danger' : 'primary'"
        >
          <el-card>
            <h4>交易ID: <el-link target="_blank" :href="`${explorerUrl}/?tab=transactions&transId=${activity.txId}`">{{ activity.txId }}</el-link></h4>

            <div v-if="!activity.isDelete && activity.value">
              <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto; max-height: 300px;">{{ JSON.stringify(activity.value, null, 2) }}</pre>
            </div>
            <p v-else>账户信息已被删除</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script>
import { getAccount, getAccountHistory } from '@/api/account'

export default {
  name: 'AccountDetail',
  data() {
    return {
      explorerUrl: process.env.VUE_APP_EXPLORER_URL,
      account: {
        accountId: '',
        orgId: '',
        accountNumber: '',
        accountType: '',
        currency: [],
        status: true,
        otherInfo: {
          fieldOne: 'some information',
          fieldTwo: true,
          fieldThree: 123.456
        },
        createdTimestamp: 0
      },
      historyList: [],
      loading: false,
      activeTab: 'basic'
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.loading = true
      const accountId = this.$route.params.id

      // 获取账户详情
      getAccount(accountId).then(response => {
        this.account = response.data

        // 获取账户历史记录
        return getAccountHistory(accountId)
      }).then(response => {
        this.historyList = response.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    goBack() {
      this.$router.push({ path: '/sample/account' })
    },
    formatAccountType(type) {
      const typeMap = {
        'SAVING': '储蓄账户',
        'CURRENT': '活期账户',
        'FEX': '外汇账户'
      }
      return typeMap[type] || type
    },
    formatTime(timestamp) {
      if (!timestamp) {
        return ''
      }
      const date = new Date(timestamp)
      return date.toLocaleString()
    }
  }
}
</script>

<style scoped>
.box-card {
  width: 100%;
  margin-bottom: 20px;
}

pre {
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>
