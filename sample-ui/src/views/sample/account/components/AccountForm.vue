<template>
  <el-form ref="accountForm" size="small" :model="accountForm" :rules="rules" label-width="120px">
    <el-form-item label="账户ID" prop="accountId">
      <el-input v-model="accountForm.accountId" :disabled="isEdit" placeholder="请输入账户ID" />
    </el-form-item>
    <el-form-item label="组织ID" prop="orgId">
      <el-input v-model="accountForm.orgId" placeholder="请输入组织ID" />
    </el-form-item>
    <el-form-item label="账户号码" prop="accountNumber">
      <el-input v-model="accountForm.accountNumber" placeholder="请输入账户号码" />
    </el-form-item>
    <el-form-item label="账户类型" prop="accountType">
      <el-select v-model="accountForm.accountType" placeholder="请选择账户类型">
        <el-option label="储蓄账户" value="SAVING" />
        <el-option label="活期账户" value="CURRENT" />
        <el-option label="外汇账户" value="FEX" />
      </el-select>
    </el-form-item>
    <el-form-item label="币种" prop="currency">
      <el-select v-model="accountForm.currency" multiple placeholder="请选择币种">
        <el-option label="人民币" value="CNY" />
        <el-option label="港币" value="HKD" />
        <el-option label="美元" value="USD" />
        <el-option label="欧元" value="EUR" />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'AccountForm',
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    account: {
      type: Object,
      default: () => ({
        accountId: '',
        orgId: '',
        accountNumber: '',
        accountType: '',
        currency: [],
        status: true,
        otherInfo: {
          fieldOne: 'some information',
          fieldTwo: true,
          fieldThree: 123.456
        }
      })
    }
  },
  data() {
    return {
      accountForm: {
        accountId: '',
        orgId: '',
        accountNumber: '',
        accountType: '',
        currency: [],
        status: true,
        otherInfo: {
          fieldOne: 'some information',
          fieldTwo: true,
          fieldThree: 123.456
        }
      },
      rules: {
        accountId: [
          { required: true, message: '请输入账户ID', trigger: 'blur' },
          { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
        ],
        orgId: [
          { required: true, message: '请输入组织ID', trigger: 'blur' }
        ],
        accountNumber: [
          { required: true, message: '请输入账户号码', trigger: 'blur' }
        ],
        accountType: [
          { required: true, message: '请选择账户类型', trigger: 'change' }
        ],
        currency: [
          { required: true, message: '请选择至少一种币种', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    account: {
      handler(val) {
        // 深拷贝防止直接修改props
        this.accountForm = JSON.parse(JSON.stringify(val))
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    submitForm() {
      console.log('Submitting form, current data:', this.accountForm)
      this.$refs.accountForm.validate(valid => {
        if (valid) {
          console.log('Form validation passed, emitting submit event with data:', this.accountForm)
          this.$emit('submitForm', this.accountForm)
        }
      })
    },
    resetForm() {
      this.$refs.accountForm.resetFields()
      if (this.isEdit) {
        // 如果是编辑模式，重置为原始数据
        this.accountForm = JSON.parse(JSON.stringify(this.account))
      }
    }
  }
}
</script>
