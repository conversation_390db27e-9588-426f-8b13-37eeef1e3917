<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.accountId"
        placeholder="账户ID"
        style="width: 200px;"
        class="filter-item"
        size="small"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orgId"
        placeholder="组织ID"
        style="width: 200px;"
        class="filter-item"
        size="small"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.accountNumber"
        placeholder="账户号码"
        style="width: 200px;"
        class="filter-item"
        size="small"
        clearable
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.accountType"
        placeholder="账户类型"
        clearable
        style="width: 150px"
        class="filter-item"
        size="small"
      >
        <el-option label="储蓄账户" value="SAVING" />
        <el-option label="活期账户" value="CURRENT" />
        <el-option label="外汇账户" value="FEX" />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 100px"
        class="filter-item"
        size="small"
      >
        <el-option label="正常" :value="true" />
        <el-option label="已删除" :value="false" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="small"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <el-button
      style="margin-bottom: 10px"
      type="primary"
      icon="el-icon-plus"
      size="small"
      @click="handleCreate"
    >
      新增
    </el-button>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="账户ID" prop="accountId" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.accountId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="组织ID" prop="orgId" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.orgId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账户号码" prop="accountNumber" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.accountNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账户类型" prop="accountType" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ formatAccountType(scope.row.accountType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="币种" prop="currency" align="center" width="150">
        <template slot-scope="scope">
          <el-tag v-for="(item, index) in scope.row.currency" :key="index" style="margin-right: 5px">
            {{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" align="center" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'">
            {{ scope.row.status ? '正常' : '已删除' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdTimestamp" align="center" width="180">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.createdTimestamp) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status" type="primary" size="mini" @click="handleUpdate(scope.row)">
            编辑
          </el-button>
          <el-button
            v-if="scope.row.status"
            size="mini"
            type="danger"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
          <el-button type="info" size="mini" @click="handleDetail(scope.row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-button
        :disabled="!hasNextPage"
        type="primary"
        size="small"
        @click="loadNextPage"
      >
        加载更多
      </el-button>
      <span v-if="list.length > 0" class="pagination-info">
        当前已加载 {{ list.length }} 条记录
      </span>
      <span v-else class="pagination-info">
        暂无数据
      </span>
    </div>

    <!-- 创建或编辑对话框 -->
    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
    >
      <account-form
        ref="accountForm"
        :is-edit="dialogStatus === 'update'"
        :account="currentAccount"
        @submitForm="dialogStatus === 'create' ? createData($event) : updateData($event)"
      />
    </el-dialog>
  </div>
</template>

<script>
import { createAccount, updateAccount, deleteAccount, getAccountPage } from '@/api/account'
import AccountForm from './components/AccountForm'

export default {
  name: 'AccountList',
  components: { AccountForm },
  data() {
    return {
      list: [],
      listLoading: true,
      listQuery: {
        accountId: undefined,
        orgId: undefined,
        accountNumber: undefined,
        accountType: undefined,
        status: undefined
      },
      currentAccount: {
        accountId: '',
        orgId: '',
        accountNumber: '',
        accountType: '',
        currency: [],
        status: true,
        otherInfo: {
          fieldOne: 'some information',
          fieldTwo: true,
          fieldThree: 123.456
        }
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑账户',
        create: '创建账户'
      },
      bookmark: '',
      pageSize: 10,
      hasNextPage: true,
      prevBookmark: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      this.list = []
      this.bookmark = ''
      this.prevBookmark = ''
      this.hasNextPage = true

      this.fetchData()
    },
    fetchData() {
      // 构建查询参数
      const params = {}
      if (this.listQuery.accountId) {
        params.accountId = this.listQuery.accountId
      }
      if (this.listQuery.orgId) {
        params.orgId = this.listQuery.orgId
      }
      if (this.listQuery.accountNumber) {
        params.accountNumber = this.listQuery.accountNumber
      }
      if (this.listQuery.accountType) {
        params.accountType = this.listQuery.accountType
      }
      if (this.listQuery.status !== undefined && this.listQuery.status !== '') {
        params.status = this.listQuery.status
      }

      // 构建分页查询请求
      const data = {
        pageSize: this.pageSize,
        bookmark: this.bookmark,
        params: params
      }

      getAccountPage(data).then(response => {
        const { data } = response

        // 将新记录添加到列表中
        this.list = [...this.list, ...data.records]

        // 保存当前bookmark用于下次查询
        this.prevBookmark = this.bookmark
        this.bookmark = data.bookmark

        // 如果返回的bookmark与上次相同，说明没有更多数据了
        if (this.bookmark === this.prevBookmark || data.records.length === 0) {
          this.hasNextPage = false
        } else {
          this.hasNextPage = true
        }

        // Just to simulate the time of the request
        setTimeout(() => {
          this.listLoading = false
        }, 500)
      }).catch(() => {
        this.listLoading = false
      })
    },
    loadNextPage() {
      if (this.hasNextPage) {
        this.listLoading = true
        this.fetchData()
      }
    },
    handleFilter() {
      this.list = []
      this.bookmark = ''
      this.prevBookmark = ''
      this.hasNextPage = true
      this.listLoading = true
      this.fetchData()
    },
    resetCurrentAccount() {
      this.currentAccount = {
        accountId: '',
        orgId: '',
        accountNumber: '',
        accountType: '',
        currency: [],
        status: true,
        otherInfo: {
          fieldOne: 'some information',
          fieldTwo: true,
          fieldThree: 123.456
        }
      }
    },
    handleCreate() {
      this.resetCurrentAccount()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['accountForm'].resetForm()
      })
    },
    createData(data) {
      console.log('Creating account with data:', data)
      this.listLoading = true
      createAccount(data).then(response => {
        console.log('Create account response:', response)
        this.dialogFormVisible = false
        this.$message.success('创建账户成功')
        this.getList()
      }).catch(error => {
        console.error('Create account error:', error)
        this.$message.error('创建账户失败: ' + (error.message || '未知错误'))
        this.listLoading = false
      })
    },
    handleUpdate(row) {
      this.currentAccount = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['accountForm'].resetForm()
      })
    },
    updateData(data) {
      console.log('Updating account with data:', data)
      this.listLoading = true
      updateAccount(data).then(response => {
        console.log('Update account response:', response)
        this.dialogFormVisible = false
        this.$message.success('更新账户成功')
        this.getList()
      }).catch(error => {
        console.error('Update account error:', error)
        this.$message.error('更新账户失败: ' + (error.message || '未知错误'))
        this.listLoading = false
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该账户吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAccount(row.accountId).then(response => {
          this.$message.success('删除账户成功')
          this.getList()
        })
      })
    },
    handleDetail(row) {
      this.$router.push({ path: `/sample/account/detail/${row.accountId}` })
    },
    formatAccountType(type) {
      const typeMap = {
        'SAVING': '储蓄账户',
        'CURRENT': '活期账户',
        'FEX': '外汇账户'
      }
      return typeMap[type] || type
    },
    formatTime(timestamp) {
      if (!timestamp) {
        return ''
      }
      const date = new Date(timestamp)
      return date.toLocaleString()
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-item {
  margin-bottom: 10px;
  margin-right: 10px;
}
.pagination-container {
  padding: 20px 0;
  display: flex;
  align-items: center;
}
.pagination-info {
  margin-left: 15px;
  color: #606266;
}
</style>
