import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'
import { Message } from 'element-ui'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: ''
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      // 获取已注册的用户列表
      const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]')

      // 默认管理员账户
      const defaultAdmin = {
        username: 'admin',
        password: 'Aa123456',
        realName: '管理员',
        email: '<EMAIL>'
      }

      // 合并默认管理员和注册用户
      const allUsers = [defaultAdmin, ...registeredUsers]

      // 查找匹配的用户
      const user = allUsers.find(u => u.username === username && u.password === password)

      if (user) {
        const token = `${username}-token`
        commit('SET_TOKEN', token)
        commit('SET_NAME', user.realName || user.username)
        setToken(token)

        // 将当前用户信息存储到localStorage
        localStorage.setItem('currentUser', JSON.stringify({
          username: user.username,
          realName: user.realName || user.username,
          email: user.email
        }))

        resolve()
      } else {
        Message({
          message: '用户名或密码错误',
          type: 'error',
          duration: 5 * 1000
        })
        reject(new Error('用户名或密码错误'))
      }
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 从 localStorage 获取当前用户信息
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')

      if (currentUser.username) {
        const data = {
          name: currentUser.realName || currentUser.username,
          avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80',
          username: currentUser.username,
          email: currentUser.email
        }

        const { name, avatar } = data
        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        resolve(data)
      } else {
        reject(new Error('Verification failed, please Login again.'))
      }
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      removeToken() // must remove  token  first
      localStorage.removeItem('currentUser') // 清除当前用户信息
      resetRouter()
      commit('RESET_STATE')
      resolve()
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      localStorage.removeItem('currentUser') // 清除当前用户信息
      commit('RESET_STATE')
      resolve()
    })
  },

  // user register
  register({ commit }, userInfo) {
    const { username, email, password, realName, phone } = userInfo
    return new Promise((resolve, reject) => {
      // 这里可以调用真实的注册API
      // register({ username: username.trim(), email: email.trim(), password, realName: realName.trim(), phone: phone.trim() }).then(response => {
      //   const { data } = response
      //   resolve(data)
      // }).catch(error => {
      //   reject(error)
      // })

      // 模拟注册逻辑
      setTimeout(() => {
        if (username && email && password && realName && phone) {
          resolve({
            message: '注册成功',
            user: {
              username: username.trim(),
              email: email.trim(),
              realName: realName.trim(),
              phone: phone.trim()
            }
          })
        } else {
          reject(new Error('注册信息不完整'))
        }
      }, 1000)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

