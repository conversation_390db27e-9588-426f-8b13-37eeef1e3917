import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'
import { Message } from 'element-ui'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: ''
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      // login({ username: username.trim(), password: password }).then(response => {
      //   const { data } = response
      //   commit('SET_TOKEN', data.token)
      //   setToken(data.token)
      //   resolve()
      // }).catch(error => {
      //   reject(error)
      // })

      if (username === 'admin' && password === 'Aa123456') {
        const token = 'admin-token'
        commit('SET_TOKEN', token)
        setToken(token)
        resolve()
      } else {
        Message({
          message: '用户名或密码错误',
          type: 'error',
          duration: 5 * 1000
        })
        reject()
      }
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      // getInfo(state.token).then(response => {
      //   const { data } = response
      //
      //   if (!data) {
      //     return reject('Verification failed, please Login again.')
      //   }
      //
      //   const { name, avatar } = data
      //
      //   commit('SET_NAME', name)
      //   commit('SET_AVATAR', avatar)
      //   resolve(data)
      // }).catch(error => {
      //   reject(error)
      // })

      const data = { name: 'admin', avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80' }
      const { name, avatar } = data
      commit('SET_NAME', name)
      commit('SET_AVATAR', avatar)
      resolve(data)
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      // logout(state.token).then(() => {
      //   removeToken() // must remove  token  first
      //   resetRouter()
      //   commit('RESET_STATE')
      //   resolve()
      // }).catch(error => {
      //   reject(error)
      // })

      removeToken() // must remove  token  first
      resetRouter()
      commit('RESET_STATE')
      resolve()
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  },

  // user register
  register({ commit }, userInfo) {
    const { username, email, password, realName, phone } = userInfo
    return new Promise((resolve, reject) => {
      // 这里可以调用真实的注册API
      // register({ username: username.trim(), email: email.trim(), password, realName: realName.trim(), phone: phone.trim() }).then(response => {
      //   const { data } = response
      //   resolve(data)
      // }).catch(error => {
      //   reject(error)
      // })

      // 模拟注册逻辑
      setTimeout(() => {
        if (username && email && password && realName && phone) {
          resolve({
            message: '注册成功',
            user: {
              username: username.trim(),
              email: email.trim(),
              realName: realName.trim(),
              phone: phone.trim()
            }
          })
        } else {
          reject(new Error('注册信息不完整'))
        }
      }, 1000)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

