import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/register',
    component: () => import('@/views/register/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: '首页', icon: 'dashboard' }
    }]
  },

  {
    path: '/sample',
    component: Layout,
    redirect: '/sample/account',
    name: 'Sample',
    meta: { title: '示例应用', icon: 'el-icon-s-help' },
    children: [
      {
        path: 'account',
        name: 'Account',
        component: () => import('@/views/sample/account/index'),
        meta: { title: '账户管理', icon: 'el-icon-user' }
      },
      {
        path: 'account/detail/:id',
        name: 'AccountDetail',
        component: () => import('@/views/sample/account/detail'),
        meta: { title: '账户详情', icon: 'el-icon-document' },
        hidden: true
      },
      {
        path: 'transfer',
        name: 'Transfer',
        component: () => import('@/views/sample/transfer/index'),
        meta: { title: '转账管理', icon: 'el-icon-money' }
      },
      {
        path: 'init-balance',
        name: 'InitBalance',
        component: () => import('@/views/sample/transfer/init-balance'),
        meta: { title: '余额初始化', icon: 'el-icon-coin' }
      },
      {
        path: 'create-fee-account',
        name: 'CreateFeeAccount',
        component: () => import('@/views/sample/transfer/create-fee-account'),
        meta: { title: '创建手续费公户', icon: 'el-icon-office-building' }
      }
    ]
  },

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-*********
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
