import request from '@/utils/request'

/**
 * Create a new account
 * @param {Object} data - Account data
 * @returns {Promise}
 */
export function createAccount(data) {
  return request({
    url: '/account',
    method: 'post',
    data
  })
}

/**
 * Update an existing account
 * @param {Object} data - Account data
 * @returns {Promise}
 */
export function updateAccount(data) {
  return request({
    url: '/account',
    method: 'put',
    data
  })
}

/**
 * Get account details by ID
 * @param {String} accountId - Account ID
 * @returns {Promise}
 */
export function getAccount(accountId) {
  return request({
    url: `/account/${accountId}`,
    method: 'get'
  })
}

/**
 * Delete an account by ID
 * @param {String} accountId - Account ID
 * @returns {Promise}
 */
export function deleteAccount(accountId) {
  return request({
    url: `/account/${accountId}`,
    method: 'delete'
  })
}

/**
 * Get account history by ID
 * @param {String} accountId - Account ID
 * @returns {Promise}
 */
export function getAccountHistory(accountId) {
  return request({
    url: `/account/history/${accountId}`,
    method: 'get'
  })
}

/**
 * Get paginated account list with search parameters
 * @param {Object} data - Search parameters, page size, and bookmark
 * @returns {Promise}
 */
export function getAccountPage(data) {
  return request({
    url: '/account/page',
    method: 'post',
    data
  })
}
