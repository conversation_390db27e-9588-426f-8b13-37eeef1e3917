import request from '@/utils/request'

/**
 * 初始化账户余额
 * @param {Object} data - 初始化参数
 * @returns {Promise}
 */
export function initAccountBalance(data) {
  return request({
    url: '/transfer/init-balance',
    method: 'post',
    data
  })
}

/**
 * 执行转账
 * @param {Object} data - 转账信息
 * @returns {Promise}
 */
export function transfer(data) {
  return request({
    url: '/transfer',
    method: 'post',
    data
  })
}

/**
 * 查询账户余额
 * @param {String} accountId - 账户ID
 * @param {String} currency - 币种
 * @returns {Promise}
 */
export function getAccountBalance(accountId, currency) {
  return request({
    url: `/transfer/balance/${accountId}/${currency}`,
    method: 'get'
  })
}

/**
 * 查询转账记录
 * @param {String} transferId - 转账ID
 * @returns {Promise}
 */
export function getTransfer(transferId) {
  return request({
    url: `/transfer/${transferId}`,
    method: 'get'
  })
}

/**
 * 查询账户的转账记录
 * @param {Object} data - 查询参数
 * @returns {Promise}
 */
export function queryTransfersByAccount(data) {
  return request({
    url: '/transfer/query-by-account',
    method: 'post',
    data
  })
}

/**
 * 查询账户所有币种余额
 * @param {String} accountId - 账户ID
 * @returns {Promise}
 */
export function getAllAccountBalances(accountId) {
  return request({
    url: `/transfer/balances/${accountId}`,
    method: 'get'
  })
}
