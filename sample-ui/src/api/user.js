import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function getInfo(token) {
  return request({
    url: '/user/info',
    method: 'get',
    params: { token }
  })
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  })
}

export function register(data) {
  // 临时mock注册功能，用于测试
  return new Promise((resolve, reject) => {
    console.log('模拟注册 API 调用:', data)

    // 模拟网络延迟
    setTimeout(() => {
      // 模拟成功响应
      if (data.username && data.email && data.password && data.realName && data.phone) {
        // 获取已注册的用户列表
        const registeredUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]')

        // 检查用户名是否已存在
        const existingUser = registeredUsers.find(user => user.username === data.username || user.email === data.email)
        if (existingUser) {
          reject(new Error('用户名或邮箱已存在'))
          return
        }

        // 添加新用户
        const newUser = {
          id: Date.now(),
          username: data.username,
          email: data.email,
          password: data.password, // 在实际应用中应该加密存储
          realName: data.realName,
          phone: data.phone,
          createdAt: new Date().toISOString()
        }

        registeredUsers.push(newUser)
        localStorage.setItem('registeredUsers', JSON.stringify(registeredUsers))

        resolve({
          code: 200,
          message: '注册成功',
          data: {
            id: newUser.id,
            username: newUser.username,
            email: newUser.email,
            realName: newUser.realName,
            phone: newUser.phone,
            createdAt: newUser.createdAt
          }
        })
      } else {
        reject(new Error('注册信息不完整'))
      }
    }, 1000)
  })

  // 真实的API调用（当后端服务可用时可以取消注释）
  // return request({
  //   url: '/user/register',
  //   method: 'post',
  //   data
  // })
}

export function checkUsername(username) {
  // 临时mock用户名检查
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟一些已存在的用户名
      const existingUsernames = ['admin', 'test', 'user123']
      if (existingUsernames.includes(username)) {
        reject(new Error('用户名已存在'))
      } else {
        resolve({ available: true })
      }
    }, 500)
  })

  // 真实的API调用
  // return request({
  //   url: '/user/check-username',
  //   method: 'get',
  //   params: { username }
  // })
}

export function checkEmail(email) {
  // 临时mock邮箱检查
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟一些已注册的邮箱
      const existingEmails = ['<EMAIL>', '<EMAIL>']
      if (existingEmails.includes(email)) {
        reject(new Error('邮箱已被注册'))
      } else {
        resolve({ available: true })
      }
    }, 500)
  })

  // 真实的API调用
  // return request({
  //   url: '/user/check-email',
  //   method: 'get',
  //   params: { email }
  // })
}
