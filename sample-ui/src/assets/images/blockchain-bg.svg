<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 渐变背景 -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 区块链网络节点 -->
  <g opacity="0.6">
    <!-- 节点 -->
    <circle cx="200" cy="200" r="8" fill="url(#nodeGradient)"/>
    <circle cx="400" cy="150" r="6" fill="url(#nodeGradient)"/>
    <circle cx="600" cy="250" r="10" fill="url(#nodeGradient)"/>
    <circle cx="800" cy="180" r="7" fill="url(#nodeGradient)"/>
    <circle cx="1000" cy="220" r="9" fill="url(#nodeGradient)"/>
    <circle cx="1200" cy="160" r="8" fill="url(#nodeGradient)"/>
    <circle cx="1400" cy="200" r="6" fill="url(#nodeGradient)"/>
    <circle cx="1600" cy="180" r="8" fill="url(#nodeGradient)"/>
    
    <circle cx="150" cy="400" r="7" fill="url(#nodeGradient)"/>
    <circle cx="350" cy="450" r="8" fill="url(#nodeGradient)"/>
    <circle cx="550" cy="380" r="6" fill="url(#nodeGradient)"/>
    <circle cx="750" cy="420" r="9" fill="url(#nodeGradient)"/>
    <circle cx="950" cy="400" r="7" fill="url(#nodeGradient)"/>
    <circle cx="1150" cy="450" r="8" fill="url(#nodeGradient)"/>
    <circle cx="1350" cy="380" r="6" fill="url(#nodeGradient)"/>
    <circle cx="1550" cy="420" r="7" fill="url(#nodeGradient)"/>
    <circle cx="1750" cy="400" r="8" fill="url(#nodeGradient)"/>
    
    <circle cx="100" cy="600" r="6" fill="url(#nodeGradient)"/>
    <circle cx="300" cy="650" r="8" fill="url(#nodeGradient)"/>
    <circle cx="500" cy="580" r="7" fill="url(#nodeGradient)"/>
    <circle cx="700" cy="620" r="9" fill="url(#nodeGradient)"/>
    <circle cx="900" cy="600" r="6" fill="url(#nodeGradient)"/>
    <circle cx="1100" cy="650" r="8" fill="url(#nodeGradient)"/>
    <circle cx="1300" cy="580" r="7" fill="url(#nodeGradient)"/>
    <circle cx="1500" cy="620" r="8" fill="url(#nodeGradient)"/>
    <circle cx="1700" cy="600" r="6" fill="url(#nodeGradient)"/>
    
    <circle cx="250" cy="800" r="8" fill="url(#nodeGradient)"/>
    <circle cx="450" cy="850" r="6" fill="url(#nodeGradient)"/>
    <circle cx="650" cy="780" r="9" fill="url(#nodeGradient)"/>
    <circle cx="850" cy="820" r="7" fill="url(#nodeGradient)"/>
    <circle cx="1050" cy="800" r="8" fill="url(#nodeGradient)"/>
    <circle cx="1250" cy="850" r="6" fill="url(#nodeGradient)"/>
    <circle cx="1450" cy="780" r="7" fill="url(#nodeGradient)"/>
    <circle cx="1650" cy="820" r="8" fill="url(#nodeGradient)"/>
    
    <!-- 连接线 -->
    <line x1="200" y1="200" x2="400" y2="150" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="400" y1="150" x2="600" y2="250" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="600" y1="250" x2="800" y2="180" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="800" y1="180" x2="1000" y2="220" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="1000" y1="220" x2="1200" y2="160" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="1200" y1="160" x2="1400" y2="200" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="1400" y1="200" x2="1600" y2="180" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    
    <line x1="150" y1="400" x2="350" y2="450" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="350" y1="450" x2="550" y2="380" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="550" y1="380" x2="750" y2="420" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="750" y1="420" x2="950" y2="400" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="950" y1="400" x2="1150" y2="450" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="1150" y1="450" x2="1350" y2="380" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="1350" y1="380" x2="1550" y2="420" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    <line x1="1550" y1="420" x2="1750" y2="400" stroke="#4facfe" stroke-width="1" opacity="0.4"/>
    
    <!-- 垂直连接 -->
    <line x1="200" y1="200" x2="150" y2="400" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="400" y1="150" x2="350" y2="450" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="600" y1="250" x2="550" y2="380" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="800" y1="180" x2="750" y2="420" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="1000" y1="220" x2="950" y2="400" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="1200" y1="160" x2="1150" y2="450" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    
    <line x1="150" y1="400" x2="100" y2="600" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="350" y1="450" x2="300" y2="650" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="550" y1="380" x2="500" y2="580" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="750" y1="420" x2="700" y2="620" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="950" y1="400" x2="900" y2="600" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
    <line x1="1150" y1="450" x2="1100" y2="650" stroke="#4facfe" stroke-width="1" opacity="0.3"/>
  </g>
  
  <!-- 装饰性几何图形 -->
  <g opacity="0.1">
    <polygon points="100,100 150,50 200,100 150,150" fill="#ffffff"/>
    <polygon points="1720,100 1770,50 1820,100 1770,150" fill="#ffffff"/>
    <polygon points="100,980 150,930 200,980 150,1030" fill="#ffffff"/>
    <polygon points="1720,980 1770,930 1820,980 1770,1030" fill="#ffffff"/>
  </g>
</svg>
