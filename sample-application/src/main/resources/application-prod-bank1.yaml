server:
  port: 8080

hyperledger:
  fabric:
    msp-id: Bank1MSP
    peer-domain: peer1.bank1.finance.com
    peer-port: 7051
    peer-address: ${hyperledger.fabric.peer-domain}:${hyperledger.fabric.peer-port}
    ca-address: https://ca.bank1.finance.com:8054
    tls-cert-path: src/main/resources/organizations/peerOrganizations/bank1.finance.com/peers/peer1.bank1.finance.com/tls/ca.crt # LBS组织的TLS CA证书
    ca-root-cert: src/main/resources/organizations/peerOrganizations/bank1.finance.com/msp/tlscacerts/tlsca.bank1.finance.com-cert.pem # LBS组织的CA根证书
    channel-name: cross-border-payment-channel
    chaincode-name: sample-chaincode
    admin-name: bank1admin
    admin-password: bank1adminpw
