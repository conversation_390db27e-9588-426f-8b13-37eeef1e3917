
#############################################################################
#   This is a configuration file for the fabric-ca-client command.
#
#   COMMAND LINE ARGUMENTS AND ENVIRONMENT VARIABLES
#   ------------------------------------------------
#   Each configuration element can be overridden via command line
#   arguments or environment variables.  The precedence for determining
#   the value of each element is as follows:
#   1) command line argument
#      Examples:
#      a) --url https://localhost:7054
#         To set the fabric-ca server url
#      b) --tls.client.certfile certfile.pem
#         To set the client certificate for TLS
#   2) environment variable
#      Examples:
#      a) FABRIC_CA_CLIENT_URL=https://localhost:7054
#         To set the fabric-ca server url
#      b) FABRIC_CA_CLIENT_TLS_CLIENT_CERTFILE=certfile.pem
#         To set the client certificate for TLS
#   3) configuration file
#   4) default value (if there is one)
#      All default values are shown beside each element below.
#
#   FILE NAME ELEMENTS
#   ------------------
#   The value of all fields whose name ends with "file" or "files" are
#   name or names of other files.
#   For example, see "tls.certfiles" and "tls.client.certfile".
#   The value of each of these fields can be a simple filename, a
#   relative path, or an absolute path.  If the value is not an
#   absolute path, it is interpretted as being relative to the location
#   of this configuration file.
#
#############################################################################

#############################################################################
# Client Configuration
#############################################################################

# URL of the Fabric-ca-server (default: http://localhost:7054)
url: https://ca.bank2.finance.com:9054

# Membership Service Provider (MSP) directory
# This is useful when the client is used to enroll a peer or orderer, so
# that the enrollment artifacts are stored in the format expected by MSP.
mspdir: msp

#############################################################################
#    TLS section for secure socket connection
#
#  certfiles - PEM-encoded list of trusted root certificate files
#  client:
#    certfile - PEM-encoded certificate file for when client authentication
#    is enabled on server
#    keyfile - PEM-encoded key file for when client authentication
#    is enabled on server
#############################################################################
tls:
  # TLS section for secure socket connection
  certfiles:
  client:
    certfile:
    keyfile:

#############################################################################
#  Certificate Signing Request section for generating the CSR for an
#  enrollment certificate (ECert)
#
#  cn - Used by CAs to determine which domain the certificate is to be generated for
#
#  keyrequest - Properties to use when generating a private key.
#     algo - key generation algorithm to use
#     size - size of key to generate
#     reusekey - reuse existing key during reenrollment
#
#  serialnumber - The serialnumber field, if specified, becomes part of the issued
#     certificate's DN (Distinguished Name).  For example, one use case for this is
#     a company with its own CA (Certificate Authority) which issues certificates
#     to its employees and wants to include the employee's serial number in the DN
#     of its issued certificates.
#     WARNING: The serialnumber field should not be confused with the certificate's
#     serial number which is set by the CA but is not a component of the
#     certificate's DN.
#
#  names -  A list of name objects. Each name object should contain at least one
#    "C", "L", "O", or "ST" value (or any combination of these) where these
#    are abbreviations for the following:
#        "C": country
#        "L": locality or municipality (such as city or town name)
#        "O": organization
#        "OU": organizational unit, such as the department responsible for owning the key;
#         it can also be used for a "Doing Business As" (DBS) name
#        "ST": the state or province
#
#    Note that the "OU" or organizational units of an ECert are always set according
#    to the values of the identities type and affiliation. OUs are calculated for an enroll
#    as OU=<type>, OU=<affiliationRoot>, ..., OU=<affiliationLeaf>. For example, an identity
#    of type "client" with an affiliation of "org1.dept2.team3" would have the following
#    organizational units: OU=client, OU=org1, OU=dept2, OU=team3
#
#  hosts - A list of host names for which the certificate should be valid
#
#############################################################################
csr:
  cn: bank2admin
  keyrequest:
    algo: ecdsa
    size: 256
    reusekey: false
  serialnumber:
  names:
    - C: US
      ST: North Carolina
      L:
      O: Hyperledger
      OU: Fabric
  hosts:
    - MiWiFi-R3P-srv

#############################################################################
#  Registration section used to register a new identity with fabric-ca server
#
#  name - Unique name of the identity
#  type - Type of identity being registered (e.g. 'peer, app, user')
#  affiliation - The identity's affiliation
#  maxenrollments - The maximum number of times the secret can be reused to enroll.
#                   Specially, -1 means unlimited; 0 means to use CA's max enrollment
#                   value.
#  attributes - List of name/value pairs of attribute for identity
#############################################################################
id:
  name:
  type:
  affiliation:
  maxenrollments: 0
  attributes:
   # - name:
   #   value:

#############################################################################
#  Enrollment section used to enroll an identity with fabric-ca server
#
#  profile - Name of the signing profile to use in issuing the certificate
#  label - Label to use in HSM operations
#############################################################################
enrollment:
  profile:
  label:

#############################################################################
# Name of the CA to connect to within the fabric-ca server
#############################################################################
caname:

#############################################################################
# BCCSP (BlockChain Crypto Service Provider) section allows to select which
# crypto implementation library to use
#############################################################################
bccsp:
    default: SW
    sw:
        hash: SHA2
        security: 256
        filekeystore:
            # The directory used for the software file-based keystore
            keystore: msp/keystore
