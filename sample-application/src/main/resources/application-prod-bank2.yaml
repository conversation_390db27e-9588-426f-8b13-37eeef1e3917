server:
  port: 9090

hyperledger:
  fabric:
    msp-id: Bank2MSP
    peer-domain: peer1.bank2.finance.com
    peer-port: 9051
    peer-address: ${hyperledger.fabric.peer-domain}:${hyperledger.fabric.peer-port}
    ca-address: https://ca.bank2.finance.com:9054
    tls-cert-path: src/main/resources/organizations/peerOrganizations/bank2.finance.com/peers/peer1.bank2.finance.com/tls/ca.crt
    ca-root-cert: src/main/resources/organizations/peerOrganizations/bank2.finance.com/msp/tlscacerts/tlsca.bank2.finance.com-cert.pem
    channel-name: cross-border-payment-channel
    chaincode-name: sample-chaincode
    admin-name: bank2admin
    admin-password: bank2adminpw
