package com.simnectz.blockchain.application.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountEntity implements Serializable {

    private String accountId;

    private String orgId;

    private String accountNumber;

    // example: SAVING, CURRENT, FEX
    private String accountType;

    // example: CNY, HKD
    private String[] currency;

    private boolean status;

    private OtherInfoEntity otherInfo;

    private long createdTimestamp;

}
