package com.simnectz.blockchain.application.sample.config;

import io.grpc.ManagedChannel;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.client.Contract;
import org.hyperledger.fabric.client.Gateway;
import org.hyperledger.fabric.client.Network;
import org.hyperledger.fabric.client.identity.Identities;
import org.hyperledger.fabric.client.identity.Signers;
import org.hyperledger.fabric.client.identity.X509Identity;
import org.hyperledger.fabric.sdk.Enrollment;
import org.hyperledger.fabric_ca.sdk.HFCAClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.Reader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class HyperledgerFabricGatewayConfig {

    private final HFCAClient hfcaClient;

    private final HyperledgerFabricProperties hyperledgerFabricProperties;

    @Bean
    public Gateway getGateway() throws Exception {
        try {
            Enrollment enrollment = hfcaClient.enroll(
                    hyperledgerFabricProperties.getAdminName(),
                    hyperledgerFabricProperties.getAdminPassword()
            );

            X509Certificate certificate = Identities.readX509Certificate(enrollment.getCert());

            Gateway gateway = Gateway.newInstance()
                    .identity(new X509Identity(hyperledgerFabricProperties.getMspId(), certificate))
                    .signer(Signers.newPrivateKeySigner(enrollment.getKey()))
                    .connection(getManagedChannel())
                    .evaluateOptions(options -> options.withDeadlineAfter(5, TimeUnit.SECONDS))
                    .endorseOptions(options -> options.withDeadlineAfter(15, TimeUnit.SECONDS))
                    .submitOptions(options -> options.withDeadlineAfter(5, TimeUnit.SECONDS))
                    .commitStatusOptions(options -> options.withDeadlineAfter(1, TimeUnit.MINUTES))
                    .connect();

            log.info("Connected fabric gateway successfully!");
            return gateway;
        } catch (Exception exception) {
            log.error("Connected fabric gateway failed: {}", exception.getMessage(), exception);
            throw exception;
        }

    }

    @Bean
    public Network getNetwork(Gateway gateway) {
        return gateway.getNetwork(hyperledgerFabricProperties.getChannelName());
    }

    @Bean(name = "accountContract")
    public Contract getUserContract(Network network) {
        return network.getContract(hyperledgerFabricProperties.getChaincodeName(), "AccountContract");
    }

    private ManagedChannel getManagedChannel() throws Exception {
        Reader tlsCertReader = Files.newBufferedReader(Paths.get(hyperledgerFabricProperties.getTlsCertPath()));
        X509Certificate tlsCert = Identities.readX509Certificate(tlsCertReader);

        return NettyChannelBuilder.forTarget(hyperledgerFabricProperties.getPeerAddress())
                .sslContext(GrpcSslContexts.forClient().trustManager(tlsCert).build())
                .overrideAuthority(hyperledgerFabricProperties.getPeerDomain())
                .build();
    }
    @Bean(name = "transferContract")
    public Contract getTransferContract(Network network) {
        return network.getContract(hyperledgerFabricProperties.getChaincodeName(), "TransferContract");
    }

}
