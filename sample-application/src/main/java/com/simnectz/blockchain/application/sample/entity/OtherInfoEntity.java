package com.simnectz.blockchain.application.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OtherInfoEntity implements Serializable {

    private String fieldOne;

    private boolean fieldTwo;

    private double fieldThree;

    // 新增字段支持手续费公户
    private String remark; // 备注

    private String accountPurpose; // 账户用途

    private boolean isSystemAccount; // 是否系统账户

}
