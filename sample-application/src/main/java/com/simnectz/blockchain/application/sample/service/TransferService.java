package com.simnectz.blockchain.application.sample.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.simnectz.blockchain.application.sample.entity.TransferEntity;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.gateway.Contract;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * 转账服务
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class TransferService {

    @Autowired
    @Qualifier("transferContract")
    private Contract transferContract;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 初始化账户余额
     *
     * @param accountId 账户ID
     * @param currency 币种
     * @param initialBalance 初始余额
     * @return 初始化结果
     */
    public String initAccountBalance(String accountId, String currency, String initialBalance) {
        try {
            log.info("调用智能合约初始化账户余额: accountId={}, currency={}, initialBalance={}",
                accountId, currency, initialBalance);

            byte[] result = transferContract.submitTransaction(
                "InitAccountBalance",
                accountId,
                currency,
                initialBalance
            );

            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("初始化账户余额失败", e);
            throw new RuntimeException("初始化账户余额失败: " + e.getMessage());
        }
    }

    /**
     * 执行转账
     *
     * @param transfer 转账信息
     * @return 转账结果
     */
    public String transfer(TransferEntity transfer) {
        try {
            log.info("调用智能合约执行转账: {}", transfer);

            String transferJson = objectMapper.writeValueAsString(transfer);

            byte[] result = transferContract.submitTransaction(
                "Transfer",
                transferJson
            );

            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("转账失败", e);
            throw new RuntimeException("转账失败: " + e.getMessage());
        }
    }

    /**
     * 查询账户余额
     *
     * @param accountId 账户ID
     * @param currency 币种
     * @return 账户余额
     */
    public String getAccountBalance(String accountId, String currency) {
        try {
            log.info("调用智能合约查询账户余额: accountId={}, currency={}", accountId, currency);

            byte[] result = transferContract.evaluateTransaction(
                "GetAccountBalance",
                accountId,
                currency
            );

            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("查询账户余额失败", e);
            throw new RuntimeException("查询账户余额失败: " + e.getMessage());
        }
    }

    /**
     * 查询转账记录
     *
     * @param transferId 转账ID
     * @return 转账记录
     */
    public String getTransfer(String transferId) {
        try {
            log.info("调用智能合约查询转账记录: transferId={}", transferId);

            byte[] result = transferContract.evaluateTransaction(
                "GetTransfer",
                transferId
            );

            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("查询转账记录失败", e);
            throw new RuntimeException("查询转账记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询账户的转账记录
     *
     * @param accountId 账户ID
     * @param pageSize 页面大小
     * @param bookmark 分页标记
     * @return 转账记录列表
     */
    public String queryTransfersByAccount(String accountId, String pageSize, String bookmark) {
        try {
            log.info("调用智能合约查询账户转账记录: accountId={}, pageSize={}, bookmark={}",
                accountId, pageSize, bookmark);

            byte[] result = transferContract.evaluateTransaction(
                "QueryTransfersByAccount",
                accountId,
                pageSize,
                bookmark
            );

            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("查询账户转账记录失败", e);
            throw new RuntimeException("查询账户转账记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询账户所有币种余额
     *
     * @param accountId 账户ID
     * @return 账户所有币种余额
     */
    public String getAllAccountBalances(String accountId) {
        try {
            log.info("查询账户所有币种余额: accountId={}", accountId);

            // 这里可以查询账户支持的所有币种，然后逐一查询余额
            // 为简化实现，这里返回常用币种的余额
            String[] currencies = {"CNY", "USD", "EUR", "HKD"};
            StringBuilder result = new StringBuilder("{\"data\":{\"balances\":[");

            for (int i = 0; i < currencies.length; i++) {
                try {
                    String balanceResult = getAccountBalance(accountId, currencies[i]);
                    if (i > 0) {
                        result.append(",");
                    }
                    result.append("{\"currency\":\"").append(currencies[i]).append("\",\"result\":").append(balanceResult).append("}");
                } catch (Exception e) {
                    // 如果某个币种余额不存在，跳过
                    log.warn("账户 {} 的 {} 余额不存在", accountId, currencies[i]);
                }
            }

            result.append("]}}");
            return result.toString();
        } catch (Exception e) {
            log.error("查询账户所有币种余额失败", e);
            throw new RuntimeException("查询账户所有币种余额失败: " + e.getMessage());
        }
    }
}
