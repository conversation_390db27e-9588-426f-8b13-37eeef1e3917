package com.simnectz.blockchain.application.sample.exception.advice;

import com.simnectz.blockchain.application.sample.constant.HttpStatusConstant;
import com.simnectz.blockchain.application.sample.exception.GlobalException;
import com.simnectz.blockchain.application.sample.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.client.*;
import org.hyperledger.fabric.protos.gateway.ErrorDetail;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(GlobalException.class)
    public ResponseEntity<ResultUtil<Void>> handlerGlobalException(GlobalException exception) {
        return ResponseEntity.ok(
                new ResultUtil<>(
                        exception.getCode(),
                        exception.getMessage()
                )
        );
    }

    @ExceptionHandler({RuntimeException.class, Exception.class})
    public ResponseEntity<ResultUtil<Void>> handlerException(Exception exception) {
        return ResponseEntity.ok(
                new ResultUtil<>(
                        HttpStatusConstant.SERVER_INTERNAL_ERROR,
                        "Internal Server Error"
                )
        );
    }

    @ExceptionHandler(value = {EndorseException.class, SubmitException.class, CommitStatusException.class})
    public ResponseEntity<ResultUtil<?>> handlerTransactionException(TransactionException e) {
        System.out.println("*** Successfully caught the error: ");
        e.printStackTrace(System.out);
        System.out.println("Transaction ID: " + e.getTransactionId());

        List<ErrorDetail> details = e.getDetails();
        String message = "Unknown Error";
        if (!details.isEmpty()) {
            System.out.println("Error Details:");
            for (ErrorDetail detail : details) {
                System.out.println("- address: " + detail.getAddress() + ", mspId: " + detail.getMspId()
                        + ", message: " + detail.getMessage());
            }
            message = details.get(0).getMessage().replace("chaincode response 500, ", "");
        }
        return ResponseEntity.ok(
                new ResultUtil<>(
                        HttpStatusConstant.BAD_REQUEST,
                        message
                )
        );
    }

    @ExceptionHandler(value = GatewayException.class)
    public ResponseEntity<ResultUtil<?>> handlerGatewayException(GatewayException e) {
        return ResponseEntity.ok(
                new ResultUtil<>(
                        HttpStatusConstant.SERVER_INTERNAL_ERROR,
                        e.getMessage()
                )
        );
    }

    @ExceptionHandler(CommitException.class)
    public ResponseEntity<ResultUtil<?>> handlerCommitException(CommitException e) {
        System.out.println("*** Successfully caught the error: " + e);
        e.printStackTrace(System.out);
        System.out.println("Transaction ID: " + e.getTransactionId());
        System.out.println("Status code: " + e.getCode());
        return ResponseEntity.ok(
                new ResultUtil<>(
                        HttpStatusConstant.BAD_REQUEST,
                        e.getMessage()
                )
        );
    }

}
