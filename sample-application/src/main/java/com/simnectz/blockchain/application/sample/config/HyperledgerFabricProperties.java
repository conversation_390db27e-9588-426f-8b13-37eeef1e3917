package com.simnectz.blockchain.application.sample.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "hyperledger.fabric")
public class HyperledgerFabricProperties {

    private String mspId;

    private String peerDomain;

    private String peerPort;

    private String peerAddress;

    private String caAddress;

    private String tlsCertPath;

    private String caRootCert;

    private String channelName;

    private String chaincodeName;

    private String adminName;

    private String adminPassword;

}
