package com.simnectz.blockchain.application.sample.config;

import lombok.RequiredArgsConstructor;
import org.hyperledger.fabric.sdk.security.CryptoSuite;
import org.hyperledger.fabric_ca.sdk.HFCAClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
@RequiredArgsConstructor
public class HyperledgerFabricCAConfig {

    private final HyperledgerFabricProperties hyperledgerFabricProperties;

    @Bean
    public HFCAClient hfcaClient() throws Exception {
        Properties properties = new Properties();
        properties.setProperty("pemFile" , hyperledgerFabricProperties.getCaRootCert());
        HFCAClient hfcaClient = HFCAClient.createNewInstance(
                hyperledgerFabricProperties.getCaAddress(),
                properties
        );
        hfcaClient.setCryptoSuite(CryptoSuite.Factory.getCryptoSuite());
        return hfcaClient;
    }

}
