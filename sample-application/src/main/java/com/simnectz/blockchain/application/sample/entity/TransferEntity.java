package com.simnectz.blockchain.application.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TransferEntity implements Serializable {

    private String transferId;

    private String fromAccountId;

    private String toAccountId;

    private BigDecimal amount;

    private String currency;

    private String transferType; // INTERNAL, EXTERNAL, CROSS_BORDER

    private String status; // PENDING, COMPLETED, FAILED, CANCELLED

    private String description;

    private String fromOrgId;

    private String toOrgId;

    private BigDecimal fee;

    private String feeType; // FIXED, PERCENTAGE

    private OtherInfoEntity otherInfo;

    private long createdTimestamp;

    private long completedTimestamp;

}
