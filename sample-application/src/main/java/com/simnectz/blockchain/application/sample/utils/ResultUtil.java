package com.simnectz.blockchain.application.sample.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultUtil<T> {

    private Integer code;

    private String message;

    private T data;

    private Long timestamp;

    private String transactionId;

    public ResultUtil(Integer code, String message) {
        this.code = code;
        this.message = message;
        this.timestamp = System.currentTimeMillis();
    }

}
