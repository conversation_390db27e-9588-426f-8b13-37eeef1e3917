package com.simnectz.blockchain.application.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountBalanceEntity implements Serializable {

    private String accountId;

    private String currency;

    private BigDecimal balance;

    private BigDecimal frozenAmount;

    private BigDecimal availableAmount;

    private long lastUpdatedTimestamp;

}
