package com.simnectz.blockchain.application.sample.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.simnectz.blockchain.application.sample.config.HyperledgerFabricProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.StringUtils;
import org.hyperledger.fabric.client.ChaincodeEvent;
import org.hyperledger.fabric.client.CloseableIterator;
import org.hyperledger.fabric.client.Network;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;

@Slf4j
@Component
@RequiredArgsConstructor
public class ChaincodeEventListener implements Runnable, ApplicationRunner {

    private final Network network;
    private final HyperledgerFabricProperties hyperledgerFabricProperties;
    private final ObjectMapper objectMapper;

    @Override
    public void run(ApplicationArguments args) {
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(1, new ThreadFactory() {
            @Override
            public Thread newThread(@Nonnull Runnable runnable) {
                Thread thread = new Thread(runnable);
                thread.setDaemon(true);
                thread.setName(this.getClass() + "chaincode event listener");
                return thread;
            }
        });
        executor.execute(this);
    }

    @Override
    public void run() {
        CloseableIterator<ChaincodeEvent> events = network.getChaincodeEvents(hyperledgerFabricProperties.getChaincodeName());
        log.info("chaincodeEvents {} ", events);

        while (events.hasNext()) {
            ChaincodeEvent event = events.next();
            if (event.getEventName().endsWith(hyperledgerFabricProperties.getMspId())) {
                printEventLog(event);
            }

            if (event.getEventName().equals("create_account_event")) {
                byte[] payload = event.getPayload();
                log.info(new String(payload));
                // example: execute some logic
            }
        }

    }

    private void printEventLog(ChaincodeEvent event) {
        log.info("{}receive chaincode event {}, transaction id: {},  block number: {}, payload: {}, ", System.lineSeparator(), event.getEventName(), event.getTransactionId(), event.getBlockNumber(), StringUtils.newStringUtf8(event.getPayload()));
    }

}
