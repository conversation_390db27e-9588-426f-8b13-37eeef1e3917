package com.simnectz.blockchain.application.sample.resource;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.simnectz.blockchain.application.sample.constant.HttpStatusConstant;
import com.simnectz.blockchain.application.sample.domain.dto.AccountPageDto;
import com.simnectz.blockchain.application.sample.entity.AccountEntity;
import com.simnectz.blockchain.application.sample.exception.GlobalException;
import com.simnectz.blockchain.application.sample.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.client.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping("/account")
public class AccountResource {

    @Resource(name = "accountContract")
    private Contract accountContract;

    @Resource
    private ObjectMapper objectMapper;

    @PostMapping
    public ResponseEntity<ResultUtil<Void>> createAccount(@RequestBody AccountEntity account) throws EndorseException, CommitException, SubmitException, CommitStatusException {
        try {
            String paramJson = objectMapper.writeValueAsString(account);

            byte[] result = accountContract
                    .submitTransaction("CreateAccount", paramJson);

            String jsonString = new String(result, StandardCharsets.UTF_8);

            ResultUtil<Void> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, Void.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException exception) {
            log.error("Create account failed: {}", exception.getMessage(), exception);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    @PutMapping
    public ResponseEntity<ResultUtil<Void>> updateAccount(@RequestBody AccountEntity account) throws EndorseException, CommitException, SubmitException, CommitStatusException {
        try {
            String paramJson = objectMapper.writeValueAsString(account);

            byte[] result = accountContract
                    .submitTransaction("UpdateAccount", paramJson);

            String jsonString = new String(result, StandardCharsets.UTF_8);

            ResultUtil<Void> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, Void.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException exception) {
            log.error("Update account failed: {}", exception.getMessage(), exception);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    @GetMapping("/{accountId}")
    public ResponseEntity<ResultUtil<AccountEntity>> getAccount(@PathVariable String accountId) throws GatewayException {
        try {
            byte[] result = accountContract
                    .evaluateTransaction("GetAccount", accountId);

            String jsonString = new String(result, StandardCharsets.UTF_8);

            ResultUtil<AccountEntity> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, AccountEntity.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException exception) {
            log.error("Get account failed: {}", exception.getMessage(), exception);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    @DeleteMapping("/{accountId}")
    public ResponseEntity<ResultUtil<Void>> deleteAccount(@PathVariable String accountId) throws EndorseException, CommitException, SubmitException, CommitStatusException {
        try {
            byte[] result = accountContract
                    .submitTransaction("DeleteAccount", accountId);

            String jsonString = new String(result, StandardCharsets.UTF_8);

            ResultUtil<Void> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, Void.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException exception) {
            log.error("Delete account failed: {}", exception.getMessage(), exception);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    @GetMapping("/history/{accountId}")
    public ResponseEntity<ResultUtil<List<Map<String, Object>>>> getAccountHistory(@PathVariable String accountId) throws GatewayException {
        try {
            byte[] result = accountContract
                    .evaluateTransaction("GetAccountHistory", accountId);

            String jsonString = new String(result, StandardCharsets.UTF_8);

            ResultUtil<List<Map<String, Object>>> resultUtil = objectMapper.readValue(
                    jsonString,
                    objectMapper.getTypeFactory().constructParametricType(
                            ResultUtil.class,
                            objectMapper.getTypeFactory().constructParametricType(
                                    List.class,
                                    Map.class
                            )
                    )
            );

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException exception) {
            log.error("Get account history failed: {}", exception.getMessage(), exception);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    @PostMapping("page")
    public ResponseEntity<ResultUtil<Map<String, Object>>> getAccountPage(@RequestBody AccountPageDto accountPage) throws EndorseException, CommitException, SubmitException, CommitStatusException {
        try {
            String paramJson = objectMapper.writeValueAsString(accountPage.getParams());

            byte[] result = accountContract
                    .submitTransaction("QueryAccounts", paramJson, String.valueOf(accountPage.getPageSize()), accountPage.getBookmark());

            String jsonString = new String(result, StandardCharsets.UTF_8);

            ResultUtil<Map<String, Object>> resultUtil = objectMapper.readValue(
                    jsonString,
                    objectMapper.getTypeFactory().constructParametricType(
                            ResultUtil.class,
                            Map.class
                    )
            );

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException exception) {
            log.error("Update account failed: {}", exception.getMessage(), exception);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

}