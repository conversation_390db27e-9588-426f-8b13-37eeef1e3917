package com.simnectz.blockchain.application.sample.resource;

import com.simnectz.blockchain.application.sample.service.TransferService;
import com.simnectz.blockchain.application.sample.entity.TransferEntity;
import com.simnectz.blockchain.application.sample.entity.AccountBalanceEntity;
import com.simnectz.blockchain.application.sample.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 转账资源控制器
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/transfer")
@Slf4j
@CrossOrigin
public class TransferResource {

    @Autowired
    private TransferService transferService;

    /**
     * 初始化账户余额
     *
     * @param params 初始化参数
     * @return 初始化结果
     */
    @PostMapping("/init-balance")
    public String initAccountBalance(@RequestBody Map<String, Object> params) {
        try {
            String accountId = (String) params.get("accountId");
            String currency = (String) params.get("currency");
            String initialBalance = (String) params.get("initialBalance");

            log.info("初始化账户余额: accountId={}, currency={}, initialBalance={}", accountId, currency, initialBalance);
            return transferService.initAccountBalance(accountId, currency, initialBalance);
        } catch (Exception e) {
            log.error("初始化账户余额失败", e);
            throw new RuntimeException("初始化账户余额失败: " + e.getMessage());
        }
    }

    /**
     * 执行转账
     *
     * @param transfer 转账信息
     * @return 转账结果
     */
    @PostMapping
    public String transfer(@RequestBody TransferEntity transfer) {
        try {
            log.info("执行转账: {}", transfer);
            return transferService.transfer(transfer);
        } catch (Exception e) {
            log.error("转账失败", e);
            throw new RuntimeException("转账失败: " + e.getMessage());
        }
    }

    /**
     * 查询账户余额
     *
     * @param accountId 账户ID
     * @param currency 币种
     * @return 账户余额
     */
    @GetMapping("/balance/{accountId}/{currency}")
    public String getAccountBalance(@PathVariable String accountId, @PathVariable String currency) {
        try {
            log.info("查询账户余额: accountId={}, currency={}", accountId, currency);
            return transferService.getAccountBalance(accountId, currency);
        } catch (Exception e) {
            log.error("查询账户余额失败", e);
            throw new RuntimeException("查询账户余额失败: " + e.getMessage());
        }
    }

    /**
     * 查询转账记录
     *
     * @param transferId 转账ID
     * @return 转账记录
     */
    @GetMapping("/{transferId}")
    public String getTransfer(@PathVariable String transferId) {
        try {
            log.info("查询转账记录: transferId={}", transferId);
            return transferService.getTransfer(transferId);
        } catch (Exception e) {
            log.error("查询转账记录失败", e);
            throw new RuntimeException("查询转账记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询账户的转账记录
     *
     * @param params 查询参数
     * @return 转账记录列表
     */
    @PostMapping("/query-by-account")
    public String queryTransfersByAccount(@RequestBody Map<String, Object> params) {
        try {
            String accountId = (String) params.get("accountId");
            Integer pageSize = (Integer) params.getOrDefault("pageSize", 10);
            String bookmark = (String) params.getOrDefault("bookmark", "");

            log.info("查询账户转账记录: accountId={}, pageSize={}, bookmark={}", accountId, pageSize, bookmark);
            return transferService.queryTransfersByAccount(accountId, pageSize.toString(), bookmark);
        } catch (Exception e) {
            log.error("查询账户转账记录失败", e);
            throw new RuntimeException("查询账户转账记录失败: " + e.getMessage());
        }
    }

    /**
     * 查询所有账户余额
     *
     * @param accountId 账户ID
     * @return 账户所有币种余额
     */
    @GetMapping("/balances/{accountId}")
    public String getAllAccountBalances(@PathVariable String accountId) {
        try {
            log.info("查询账户所有余额: accountId={}", accountId);
            return transferService.getAllAccountBalances(accountId);
        } catch (Exception e) {
            log.error("查询账户所有余额失败", e);
            throw new RuntimeException("查询账户所有余额失败: " + e.getMessage());
        }
    }
}
