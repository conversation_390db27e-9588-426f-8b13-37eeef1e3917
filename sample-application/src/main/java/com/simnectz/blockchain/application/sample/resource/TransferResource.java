package com.simnectz.blockchain.application.sample.resource;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.simnectz.blockchain.application.sample.entity.TransferEntity;
import com.simnectz.blockchain.application.sample.entity.AccountBalanceEntity;
import com.simnectz.blockchain.application.sample.utils.ResultUtil;
import com.simnectz.blockchain.application.sample.constant.HttpStatusConstant;
import com.simnectz.blockchain.application.sample.exception.GlobalException;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.client.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 转账资源控制器
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/transfer")
@Slf4j
@CrossOrigin
public class TransferResource {

    @Resource(name = "transferContract")
    private Contract transferContract;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 初始化账户余额
     *
     * @param params 初始化参数
     * @return 初始化结果
     */
    @PostMapping("/init-balance")
    public ResponseEntity<ResultUtil<Void>> initAccountBalance(@RequestBody Map<String, Object> params) throws EndorseException, CommitException, SubmitException, CommitStatusException {
        try {
            String accountId = (String) params.get("accountId");
            String currency = (String) params.get("currency");
            String initialBalance = (String) params.get("initialBalance");

            log.info("初始化账户余额: accountId={}, currency={}, initialBalance={}", accountId, currency, initialBalance);

            byte[] result = transferContract.submitTransaction(
                    "InitAccountBalance",
                    accountId,
                    currency,
                    initialBalance
            );

            String jsonString = new String(result, StandardCharsets.UTF_8);
            ResultUtil<Void> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, Void.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException e) {
            log.error("初始化账户余额失败", e);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    /**
     * 执行转账
     *
     * @param transfer 转账信息
     * @return 转账结果
     */
    @PostMapping
    public ResponseEntity<ResultUtil<Void>> transfer(@RequestBody TransferEntity transfer) throws EndorseException, CommitException, SubmitException, CommitStatusException {
        try {
            log.info("执行转账: {}", transfer);

            String transferJson = objectMapper.writeValueAsString(transfer);

            byte[] result = transferContract.submitTransaction(
                    "Transfer",
                    transferJson
            );

            String jsonString = new String(result, StandardCharsets.UTF_8);
            ResultUtil<Void> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, Void.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException e) {
            log.error("转账失败", e);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    /**
     * 查询账户余额
     *
     * @param accountId 账户ID
     * @param currency 币种
     * @return 账户余额
     */
    @GetMapping("/balance/{accountId}/{currency}")
    public ResponseEntity<ResultUtil<AccountBalanceEntity>> getAccountBalance(@PathVariable String accountId, @PathVariable String currency) throws GatewayException {
        try {
            log.info("查询账户余额: accountId={}, currency={}", accountId, currency);

            byte[] result = transferContract.evaluateTransaction(
                    "GetAccountBalance",
                    accountId,
                    currency
            );

            String jsonString = new String(result, StandardCharsets.UTF_8);
            ResultUtil<AccountBalanceEntity> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, AccountBalanceEntity.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException e) {
            log.error("查询账户余额失败", e);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    /**
     * 查询转账记录
     *
     * @param transferId 转账ID
     * @return 转账记录
     */
    @GetMapping("/{transferId}")
    public ResponseEntity<ResultUtil<TransferEntity>> getTransfer(@PathVariable String transferId) throws GatewayException {
        try {
            log.info("查询转账记录: transferId={}", transferId);

            byte[] result = transferContract.evaluateTransaction(
                    "GetTransfer",
                    transferId
            );

            String jsonString = new String(result, StandardCharsets.UTF_8);
            ResultUtil<TransferEntity> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, TransferEntity.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException e) {
            log.error("查询转账记录失败", e);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    /**
     * 查询账户的转账记录
     *
     * @param params 查询参数
     * @return 转账记录列表
     */
    @PostMapping("/query-by-account")
    public ResponseEntity<ResultUtil<Map<String, Object>>> queryTransfersByAccount(@RequestBody Map<String, Object> params) throws GatewayException {
        try {
            String accountId = (String) params.get("accountId");
            Integer pageSize = (Integer) params.getOrDefault("pageSize", 10);
            String bookmark = (String) params.getOrDefault("bookmark", "");

            log.info("查询账户转账记录: accountId={}, pageSize={}, bookmark={}", accountId, pageSize, bookmark);

            byte[] result = transferContract.evaluateTransaction(
                    "QueryTransfersByAccount",
                    accountId,
                    pageSize.toString(),
                    bookmark
            );

            String jsonString = new String(result, StandardCharsets.UTF_8);
            ResultUtil<Map<String, Object>> resultUtil = objectMapper.readValue(jsonString, objectMapper.getTypeFactory().constructParametricType(ResultUtil.class, Map.class));

            return ResponseEntity.ok(resultUtil);
        } catch (JsonProcessingException e) {
            log.error("查询账户转账记录失败", e);
            throw new GlobalException(HttpStatusConstant.SERVER_INTERNAL_ERROR, "Internal Server Error");
        }
    }

    /**
     * 查询所有账户余额
     *
     * @param accountId 账户ID
     * @return 账户所有币种余额
     */
    @GetMapping("/balances/{accountId}")
    public String getAllAccountBalances(@PathVariable String accountId) {
        log.info("查询账户所有余额: accountId={}", accountId);

        // 这里可以查询账户支持的所有币种，然后逐一查询余额
        // 为简化实现，这里返回常用币种的余额
        String[] currencies = {"CNY", "USD", "EUR", "HKD"};
        StringBuilder result = new StringBuilder("{\"data\":{\"balances\":[");

        for (int i = 0; i < currencies.length; i++) {
            try {
                ResponseEntity<ResultUtil<AccountBalanceEntity>> balanceResponse = getAccountBalance(accountId, currencies[i]);
                if (i > 0) {
                    result.append(",");
                }
                String balanceJson = objectMapper.writeValueAsString(balanceResponse.getBody());
                result.append("{\"currency\":\"").append(currencies[i]).append("\",\"result\":").append(balanceJson).append("}");
            } catch (Exception e) {
                // 如果某个币种余额不存在，跳过
                log.warn("账户 {} 的 {} 余额不存在", accountId, currencies[i]);
            }
        }

        result.append("]}}");
        return result.toString();
    }
}
