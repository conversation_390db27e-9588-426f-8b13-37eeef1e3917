package com.simnectz.blockchain.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hyperledger.fabric.contract.annotation.DataType;
import org.hyperledger.fabric.contract.annotation.Property;

import java.io.Serializable;

@Data
@DataType
@NoArgsConstructor
@AllArgsConstructor
public class OtherInfoEntity implements Serializable {

    @Property
    private String fieldOne;

    @Property
    private boolean fieldTwo;

    @Property
    private double fieldThree;

}
