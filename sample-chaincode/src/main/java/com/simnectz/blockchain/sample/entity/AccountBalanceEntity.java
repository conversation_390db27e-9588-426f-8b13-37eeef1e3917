package com.simnectz.blockchain.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hyperledger.fabric.contract.annotation.DataType;
import org.hyperledger.fabric.contract.annotation.Property;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@DataType
@NoArgsConstructor
@AllArgsConstructor
public class AccountBalanceEntity implements Serializable {

    @Property
    private String accountId;

    @Property
    private String currency;

    @Property
    private BigDecimal balance;

    @Property
    private BigDecimal frozenAmount;

    @Property
    private BigDecimal availableAmount;

    @Property
    private long lastUpdatedTimestamp;

}
