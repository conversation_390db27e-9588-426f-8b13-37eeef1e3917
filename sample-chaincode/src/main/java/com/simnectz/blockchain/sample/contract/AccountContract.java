package com.simnectz.blockchain.sample.contract;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.simnectz.blockchain.sample.entity.AccountEntity;
import com.simnectz.blockchain.sample.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.contract.Context;
import org.hyperledger.fabric.contract.ContractInterface;
import org.hyperledger.fabric.contract.annotation.Contact;
import org.hyperledger.fabric.contract.annotation.Contract;
import org.hyperledger.fabric.contract.annotation.Default;
import org.hyperledger.fabric.contract.annotation.Info;
import org.hyperledger.fabric.contract.annotation.License;
import org.hyperledger.fabric.contract.annotation.Transaction;
import org.hyperledger.fabric.shim.ChaincodeException;
import org.hyperledger.fabric.shim.ChaincodeStub;
import org.hyperledger.fabric.shim.ledger.KeyModification;
import org.hyperledger.fabric.shim.ledger.KeyValue;
import org.hyperledger.fabric.shim.ledger.QueryResultsIterator;
import org.hyperledger.fabric.shim.ledger.QueryResultsIteratorWithMetadata;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账户合约
 * <p>
 * 该合约实现了账户的CRUD操作、分页查询、根据ID查询详情和查询账本历史记录的功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Contract(
        name = "AccountContract",
        info = @Info(
                title = "账户合约",
                description = "账户管理的智能合约，提供CRUD、分页查询和历史记录查询功能",
                version = "1.0",
                license = @License(
                        name = "Apache 2.0 License",
                        url = "https://www.apache.org/licenses/LICENSE-2.0.html"),
                contact = @Contact(
                        email = "<EMAIL>",
                        name = "AccountContract",
                        url = "https://hyperledger.example.com")
        )
)
@Default
@Slf4j
public class AccountContract implements ContractInterface {

    // 账户对象在账本中的前缀
    private static final String ACCOUNT_PREFIX = "ACCOUNT_";

    // 成功状态码
    private static final Integer SUCCESS_CODE = 20000;

    @Transaction(name = "Init", intent = Transaction.TYPE.SUBMIT)
    public String init(final Context ctx) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "合约初始化成功", txTimestamp, stub.getTxId()));
    }

    /**
     * 创建账户
     *
     * @param ctx 交易上下文
     * @param account 账户信息
     * @return 创建结果
     */
    @Transaction(name = "CreateAccount", intent = Transaction.TYPE.SUBMIT)
    public String createAccount(final Context ctx, final AccountEntity account) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        
        try {
            // 检查账户ID是否已存在
            String accountKey = ACCOUNT_PREFIX + account.getAccountId();
            String existingAccountJson = stub.getStringState(accountKey);
            if (existingAccountJson != null && !existingAccountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 已存在", account.getAccountId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            
            // 设置创建时间戳
            account.setCreatedTimestamp(txTimestamp);
            
            // 将账户对象序列化为JSON并存储到账本
            stub.putStringState(accountKey, JSON.toJSONString(account));
            
            log.info("账户创建成功: {}", account.getAccountId());

            // 设置和约事件
            stub.setEvent("create_account_event", JSON.toJSONString(account).getBytes(StandardCharsets.UTF_8));

            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "账户创建成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("创建账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 根据ID查询账户详情
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @return 账户详情
     */
    @Transaction(name = "GetAccount", intent = Transaction.TYPE.EVALUATE)
    public String getAccount(final Context ctx, final String accountId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        
        try {
            // 构建账户键
            String accountKey = ACCOUNT_PREFIX + accountId;
            
            // 从账本获取账户数据
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在", accountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            
            // 解析账户数据
            AccountEntity account = JSON.parseObject(accountJson, AccountEntity.class);
            
            log.info("查询账户成功: {}", accountId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询账户成功", account, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 更新账户信息
     *
     * @param ctx 交易上下文
     * @param account 账户信息
     * @return 更新结果
     */
    @Transaction(name = "UpdateAccount", intent = Transaction.TYPE.SUBMIT)
    public String updateAccount(final Context ctx, final AccountEntity account) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        
        try {
            // 检查账户ID是否存在
            String accountKey = ACCOUNT_PREFIX + account.getAccountId();
            String existingAccountJson = stub.getStringState(accountKey);
            if (existingAccountJson == null || existingAccountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在，无法更新", account.getAccountId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            
            // 解析现有账户数据
            AccountEntity existingAccount = JSON.parseObject(existingAccountJson, AccountEntity.class);
            
            // 保留创建时间戳
            account.setCreatedTimestamp(existingAccount.getCreatedTimestamp());
            
            // 将更新后的账户对象序列化为JSON并存储到账本
            stub.putStringState(accountKey, JSON.toJSONString(account));
            
            log.info("账户更新成功: {}", account.getAccountId());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "账户更新成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("更新账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 删除账户（逻辑删除）
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @return 删除结果
     */
    @Transaction(name = "DeleteAccount", intent = Transaction.TYPE.SUBMIT)
    public String deleteAccount(final Context ctx, final String accountId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        
        try {
            // 构建账户键
            String accountKey = ACCOUNT_PREFIX + accountId;
            
            // 从账本获取账户数据
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在，无法删除", accountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            
            // 解析账户数据
            AccountEntity account = JSON.parseObject(accountJson, AccountEntity.class);
            
            // 逻辑删除：将状态设置为false
            account.setStatus(false);
            
            // 将更新后的账户对象序列化为JSON并存储到账本
            stub.putStringState(accountKey, JSON.toJSONString(account));
            
            log.info("账户删除成功: {}", accountId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "账户删除成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("删除账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 根据条件分页查询账户
     *
     * @param ctx 交易上下文
     * @param queryString 查询条件JSON字符串
     * @param pageSize 每页记录数
     * @param bookmark 分页书签
     * @return 查询结果
     */
    @Transaction(name = "QueryAccounts", intent = Transaction.TYPE.EVALUATE)
    public String queryAccounts(final Context ctx, final String queryString, final int pageSize, final String bookmark) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        
        try {
            // 解析查询条件
            JSONObject queryJson = JSON.parseObject(queryString);
            
            // 构建CouchDB查询语句
            StringBuilder queryBuilder = new StringBuilder("{\"selector\":{");
            
            // 添加前缀条件，确保只查询账户对象
            queryBuilder.append("\"_id\":{\"$regex\":\"^").append(ACCOUNT_PREFIX).append("\"}");
            
            // 添加其他查询条件
            if (queryJson != null && !queryJson.isEmpty()) {
                for (Map.Entry<String, Object> entry : queryJson.entrySet()) {
                    queryBuilder.append(",\"").append(entry.getKey()).append("\":");
                    
                    // 根据值类型构建不同的查询条件
                    if (entry.getValue() instanceof String) {
                        queryBuilder.append("\"").append(entry.getValue()).append("\"");
                    } else {
                        queryBuilder.append(entry.getValue());
                    }
                }
            }
            
            queryBuilder.append("}}");
            
            // 执行分页查询
            QueryResultsIteratorWithMetadata<KeyValue> queryResults = stub.getQueryResultWithPagination(queryBuilder.toString(), pageSize, bookmark);
            
            // 处理查询结果
            List<AccountEntity> accounts = new ArrayList<>();
            for (KeyValue kv : queryResults) {
                AccountEntity account = JSON.parseObject(kv.getStringValue(), AccountEntity.class);
                accounts.add(account);
            }
            
            // 构建分页结果
            Map<String, Object> paginationResult = new HashMap<>();
            paginationResult.put("records", accounts);
            paginationResult.put("recordCount", accounts.size());
            paginationResult.put("bookmark", queryResults.getMetadata().getBookmark());
            paginationResult.put("fetchedRecordsCount", queryResults.getMetadata().getFetchedRecordsCount());
            
            log.info("查询账户成功，返回记录数: {}", accounts.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询账户成功", paginationResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 获取账户的历史记录
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @return 历史记录
     */
    @Transaction(name = "GetAccountHistory", intent = Transaction.TYPE.EVALUATE)
    public String getAccountHistory(final Context ctx, final String accountId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        
        try {
            // 构建账户键
            String accountKey = ACCOUNT_PREFIX + accountId;
            
            // 检查账户是否存在
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在，无法查询历史记录", accountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            
            // 获取历史记录
            QueryResultsIterator<KeyModification> historyIterator = stub.getHistoryForKey(accountKey);
            
            // 处理历史记录
            List<Map<String, Object>> historyList = new ArrayList<>();
            for (KeyModification modification : historyIterator) {
                Map<String, Object> historyItem = new HashMap<>();
                historyItem.put("txId", modification.getTxId());
                historyItem.put("timestamp", modification.getTimestamp().toEpochMilli());
                historyItem.put("isDelete", modification.isDeleted());
                
                // 解析账户数据
                if (!modification.isDeleted() && modification.getValue() != null) {
                    AccountEntity account = JSON.parseObject(modification.getStringValue(), AccountEntity.class);
                    historyItem.put("value", account);
                }
                
                historyList.add(historyItem);
            }
            
            log.info("查询账户历史记录成功: {}, 记录数: {}", accountId, historyList.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询账户历史记录成功", historyList, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询账户历史记录失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

}
