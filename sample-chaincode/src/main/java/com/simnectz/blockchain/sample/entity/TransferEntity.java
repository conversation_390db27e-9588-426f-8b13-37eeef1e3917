package com.simnectz.blockchain.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hyperledger.fabric.contract.annotation.DataType;
import org.hyperledger.fabric.contract.annotation.Property;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@DataType
@NoArgsConstructor
@AllArgsConstructor
public class TransferEntity implements Serializable {

    @Property
    private String transferId;

    @Property
    private String fromAccountId;

    @Property
    private String toAccountId;

    @Property
    private BigDecimal amount;

    @Property
    private String currency;

    @Property
    private String transferType; // INTERNAL, EXTERNAL, CROSS_BORDER

    @Property
    private String status; // PENDING, COMPLETED, FAILED, CANCELLED

    @Property
    private String description;

    @Property
    private String fromOrgId;

    @Property
    private String toOrgId;

    @Property
    private BigDecimal fee;

    @Property
    private String feeType; // FIXED, PERCENTAGE

    @Property
    private OtherInfoEntity otherInfo;

    @Property
    private long createdTimestamp;

    @Property
    private long completedTimestamp;

}
