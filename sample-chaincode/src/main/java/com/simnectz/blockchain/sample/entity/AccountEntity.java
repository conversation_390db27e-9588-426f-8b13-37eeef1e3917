package com.simnectz.blockchain.sample.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hyperledger.fabric.contract.annotation.DataType;
import org.hyperledger.fabric.contract.annotation.Property;

import java.io.Serializable;

@Data
@DataType
@NoArgsConstructor
@AllArgsConstructor
public class AccountEntity implements Serializable {

    @Property
    private String accountId;

    @Property
    private String orgId;

    @Property
    private String accountNumber;

    @Property
    // example: SAVING, CURRENT, FEX
    private String accountType;

    @Property
    // example: CNY, HKD
    private String[] currency;

    @Property
    private boolean status;

    @Property
    private OtherInfoEntity otherInfo;

    @Property
    private long createdTimestamp;

}
