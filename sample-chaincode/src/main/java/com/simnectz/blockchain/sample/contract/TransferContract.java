package com.simnectz.blockchain.sample.contract;

import com.alibaba.fastjson.JSON;
import com.simnectz.blockchain.sample.entity.AccountBalanceEntity;
import com.simnectz.blockchain.sample.entity.AccountEntity;
import com.simnectz.blockchain.sample.entity.TransferEntity;
import com.simnectz.blockchain.sample.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.contract.Context;
import org.hyperledger.fabric.contract.ContractInterface;
import org.hyperledger.fabric.contract.annotation.*;
import org.hyperledger.fabric.shim.ChaincodeException;
import org.hyperledger.fabric.shim.ChaincodeStub;
import org.hyperledger.fabric.shim.ledger.KeyValue;
import org.hyperledger.fabric.shim.ledger.QueryResultsIterator;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 转账合约
 * <p>
 * 该合约实现了账户间转账、余额管理、转账记录查询等功能
 *
 * <AUTHOR>
 * @version 1.0
 */
@Contract(
        name = "TransferContract",
        info = @Info(
                title = "转账合约",
                description = "转账管理的智能合约，提供转账、余额管理和转账记录查询功能",
                version = "1.0",
                license = @License(
                        name = "Apache 2.0 License",
                        url = "https://www.apache.org/licenses/LICENSE-2.0.html"),
                contact = @Contact(
                        email = "<EMAIL>",
                        name = "TransferContract",
                        url = "https://hyperledger.example.com")
        )
)
@Default
@Slf4j
public class TransferContract implements ContractInterface {

    // 转账记录在账本中的前缀
    private static final String TRANSFER_PREFIX = "TRANSFER_";

    // 账户余额在账本中的前缀
    private static final String BALANCE_PREFIX = "BALANCE_";

    // 账户在账本中的前缀
    private static final String ACCOUNT_PREFIX = "ACCOUNT_";

    // 成功状态码
    private static final Integer SUCCESS_CODE = 20000;

    @Transaction(name = "Init", intent = Transaction.TYPE.SUBMIT)
    public String init(final Context ctx) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "转账合约初始化成功", txTimestamp, stub.getTxId()));
    }

    /**
     * 初始化账户余额
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @param currency 币种
     * @param initialBalance 初始余额
     * @return 初始化结果
     */
    @Transaction(name = "InitAccountBalance", intent = Transaction.TYPE.SUBMIT)
    public String initAccountBalance(final Context ctx, final String accountId, final String currency, final String initialBalance) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 检查账户是否存在
            String accountKey = ACCOUNT_PREFIX + accountId;
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                throw new ChaincodeException("账户不存在: " + accountId);
            }

            BigDecimal balance = new BigDecimal(initialBalance);
            if (balance.compareTo(BigDecimal.ZERO) < 0) {
                throw new ChaincodeException("初始余额不能为负数");
            }

            String balanceKey = BALANCE_PREFIX + accountId + "_" + currency;
            String existingBalanceJson = stub.getStringState(balanceKey);
            if (existingBalanceJson != null && !existingBalanceJson.isEmpty()) {
                throw new ChaincodeException("账户余额已存在: " + accountId + "_" + currency);
            }

            AccountBalanceEntity accountBalance = new AccountBalanceEntity();
            accountBalance.setAccountId(accountId);
            accountBalance.setCurrency(currency);
            accountBalance.setBalance(balance);
            accountBalance.setFrozenAmount(BigDecimal.ZERO);
            accountBalance.setAvailableAmount(balance);
            accountBalance.setLastUpdatedTimestamp(txTimestamp);

            stub.putStringState(balanceKey, JSON.toJSONString(accountBalance));

            log.info("账户余额初始化成功: {} {} {}", accountId, currency, balance);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "账户余额初始化成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("初始化账户余额失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 执行转账
     *
     * @param ctx 交易上下文
     * @param transferJson 转账信息JSON字符串
     * @return 转账结果
     */
    @Transaction(name = "Transfer", intent = Transaction.TYPE.SUBMIT)
    public String transfer(final Context ctx, final String transferJson) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 解析JSON为对象
            TransferEntity transfer = JSON.parseObject(transferJson, TransferEntity.class);

            // 验证转账参数
            validateTransfer(transfer);

            // 检查转账ID是否已存在
            String transferKey = TRANSFER_PREFIX + transfer.getTransferId();
            String existingTransferJson = stub.getStringState(transferKey);
            if (existingTransferJson != null && !existingTransferJson.isEmpty()) {
                throw new ChaincodeException("转账ID已存在: " + transfer.getTransferId());
            }

            // 检查账户是否存在
            validateAccountExists(stub, transfer.getFromAccountId());
            validateAccountExists(stub, transfer.getToAccountId());

            // 获取转出账户余额
            String fromBalanceKey = BALANCE_PREFIX + transfer.getFromAccountId() + "_" + transfer.getCurrency();
            AccountBalanceEntity fromBalance = getAccountBalance(stub, fromBalanceKey);

            // 获取转入账户余额
            String toBalanceKey = BALANCE_PREFIX + transfer.getToAccountId() + "_" + transfer.getCurrency();
            AccountBalanceEntity toBalance = getAccountBalance(stub, toBalanceKey);

            // 检查余额是否足够
            BigDecimal totalAmount = transfer.getAmount().add(transfer.getFee());
            if (fromBalance.getAvailableAmount().compareTo(totalAmount) < 0) {
                throw new ChaincodeException("余额不足");
            }

            // 执行转账
            // 1. 转出账户扣除转账金额 + 手续费
            fromBalance.setBalance(fromBalance.getBalance().subtract(totalAmount));
            fromBalance.setAvailableAmount(fromBalance.getAvailableAmount().subtract(totalAmount));
            fromBalance.setLastUpdatedTimestamp(txTimestamp);

            // 2. 转入账户增加转账金额（不包含手续费）
            toBalance.setBalance(toBalance.getBalance().add(transfer.getAmount()));
            toBalance.setAvailableAmount(toBalance.getAvailableAmount().add(transfer.getAmount()));
            toBalance.setLastUpdatedTimestamp(txTimestamp);

            // 3. 处理手续费（如果有手续费且指定了手续费账户）
            if (transfer.getFee().compareTo(BigDecimal.ZERO) > 0 && transfer.getFeeAccountId() != null && !transfer.getFeeAccountId().isEmpty()) {
                // 获取手续费账户余额
                String feeBalanceKey = BALANCE_PREFIX + transfer.getFeeAccountId() + "_" + transfer.getCurrency();
                try {
                    AccountBalanceEntity feeBalance = getAccountBalance(stub, feeBalanceKey);
                    // 手续费账户增加手续费
                    feeBalance.setBalance(feeBalance.getBalance().add(transfer.getFee()));
                    feeBalance.setAvailableAmount(feeBalance.getAvailableAmount().add(transfer.getFee()));
                    feeBalance.setLastUpdatedTimestamp(txTimestamp);
                    stub.putStringState(feeBalanceKey, JSON.toJSONString(feeBalance));

                    log.info("手续费 {} {} 已转入账户 {}", transfer.getFee(), transfer.getCurrency(), transfer.getFeeAccountId());
                } catch (Exception e) {
                    log.warn("手续费账户 {} 不存在或处理失败，手续费将被销毁: {}", transfer.getFeeAccountId(), e.getMessage());
                    // 如果手续费账户不存在，手续费将被“销毁”（从系统中消失）
                }
            } else if (transfer.getFee().compareTo(BigDecimal.ZERO) > 0) {
                log.info("未指定手续费账户，手续费 {} {} 将被销毁", transfer.getFee(), transfer.getCurrency());
            }

            // 更新余额
            stub.putStringState(fromBalanceKey, JSON.toJSONString(fromBalance));
            stub.putStringState(toBalanceKey, JSON.toJSONString(toBalance));

            // 保存转账记录
            transfer.setStatus("COMPLETED");
            transfer.setCreatedTimestamp(txTimestamp);
            transfer.setCompletedTimestamp(txTimestamp);
            stub.putStringState(transferKey, JSON.toJSONString(transfer));

            // 为了优化查询，我们也保存复合键索引
            // 从账户的索引
            String fromAccountIndexKey = "FROM_ACCOUNT~" + transfer.getFromAccountId() + "~" + txTimestamp + "~" + transfer.getTransferId();
            stub.putStringState(fromAccountIndexKey, transfer.getTransferId());

            // 到账户的索引
            String toAccountIndexKey = "TO_ACCOUNT~" + transfer.getToAccountId() + "~" + txTimestamp + "~" + transfer.getTransferId();
            stub.putStringState(toAccountIndexKey, transfer.getTransferId());

            // 设置转账事件
            stub.setEvent("transfer_completed_event", JSON.toJSONString(transfer).getBytes(StandardCharsets.UTF_8));

            log.info("转账成功: {} -> {}, 金额: {} {}",
                    transfer.getFromAccountId(), transfer.getToAccountId(), transfer.getAmount(), transfer.getCurrency());

            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "转账成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("转账失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 查询账户余额
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @param currency 币种
     * @return 账户余额
     */
    @Transaction(name = "GetAccountBalance", intent = Transaction.TYPE.EVALUATE)
    public String getAccountBalance(final Context ctx, final String accountId, final String currency) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            String balanceKey = BALANCE_PREFIX + accountId + "_" + currency;
            AccountBalanceEntity balance = getAccountBalance(stub, balanceKey);

            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询成功", balance, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询账户余额失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 查询转账记录
     *
     * @param ctx 交易上下文
     * @param transferId 转账ID
     * @return 转账记录
     */
    @Transaction(name = "GetTransfer", intent = Transaction.TYPE.EVALUATE)
    public String getTransfer(final Context ctx, final String transferId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            String transferKey = TRANSFER_PREFIX + transferId;
            String transferJson = stub.getStringState(transferKey);

            if (transferJson == null || transferJson.isEmpty()) {
                throw new ChaincodeException("转账记录不存在: " + transferId);
            }

            TransferEntity transfer = JSON.parseObject(transferJson, TransferEntity.class);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询成功", transfer, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询转账记录失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 查询账户的转账记录
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @param pageSize 页面大小
     * @param bookmark 分页标记
     * @return 转账记录列表
     */
    @Transaction(name = "QueryTransfersByAccount", intent = Transaction.TYPE.EVALUATE)
    public String queryTransfersByAccount(final Context ctx, final String accountId, final String pageSize, final String bookmark) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            int pageSizeInt = Integer.parseInt(pageSize);

            List<TransferEntity> transfers = new ArrayList<>();
            Set<String> transferIds = new HashSet<>();

            // 查询从账户的转账记录
            String fromStartKey = "FROM_ACCOUNT~" + accountId + "~";
            String fromEndKey = "FROM_ACCOUNT~" + accountId + "~\uFFFF";

            QueryResultsIterator<KeyValue> fromIterator = stub.getStateByRange(fromStartKey, fromEndKey);
            for (KeyValue result : fromIterator) {
                String transferId = result.getStringValue();
                if (!transferIds.contains(transferId)) {
                    transferIds.add(transferId);
                    String transferKey = TRANSFER_PREFIX + transferId;
                    String transferJson = stub.getStringState(transferKey);
                    if (transferJson != null && !transferJson.isEmpty()) {
                        TransferEntity transfer = JSON.parseObject(transferJson, TransferEntity.class);
                        transfers.add(transfer);
                    }
                }
                if (transfers.size() >= pageSizeInt) {
                    break;
                }
            }

            // 如果还没有达到页面大小，继续查询到账户的转账记录
            if (transfers.size() < pageSizeInt) {
                String toStartKey = "TO_ACCOUNT~" + accountId + "~";
                String toEndKey = "TO_ACCOUNT~" + accountId + "~\uFFFF";

                QueryResultsIterator<KeyValue> toIterator = stub.getStateByRange(toStartKey, toEndKey);
                for (KeyValue result : toIterator) {
                    String transferId = result.getStringValue();
                    if (!transferIds.contains(transferId)) {
                        transferIds.add(transferId);
                        String transferKey = TRANSFER_PREFIX + transferId;
                        String transferJson = stub.getStringState(transferKey);
                        if (transferJson != null && !transferJson.isEmpty()) {
                            TransferEntity transfer = JSON.parseObject(transferJson, TransferEntity.class);
                            transfers.add(transfer);
                        }
                    }
                    if (transfers.size() >= pageSizeInt) {
                        break;
                    }
                }
            }

            // 按时间排序（降序）
            transfers.sort((t1, t2) -> Long.compare(t2.getCreatedTimestamp(), t1.getCreatedTimestamp()));

            // 截取指定数量
            if (transfers.size() > pageSizeInt) {
                transfers = transfers.subList(0, pageSizeInt);
            }

            // 生成新的bookmark（简化处理）
            String newBookmark = bookmark;
            if (transfers.size() == pageSizeInt) {
                newBookmark = String.valueOf(System.currentTimeMillis());
            }

            Map<String, Object> response = new HashMap<>();
            response.put("records", transfers);
            response.put("fetchedRecordsCount", transfers.size());
            response.put("bookmark", newBookmark);

            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询成功", response, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询转账记录失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    // 私有辅助方法

    private void validateTransfer(TransferEntity transfer) {
        if (transfer.getFromAccountId() == null || transfer.getFromAccountId().isEmpty()) {
            throw new ChaincodeException("转出账户ID不能为空");
        }
        if (transfer.getToAccountId() == null || transfer.getToAccountId().isEmpty()) {
            throw new ChaincodeException("转入账户ID不能为空");
        }
        if (transfer.getFromAccountId().equals(transfer.getToAccountId())) {
            throw new ChaincodeException("转出账户和转入账户不能相同");
        }
        if (transfer.getAmount() == null || transfer.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ChaincodeException("转账金额必须大于0");
        }
        if (transfer.getCurrency() == null || transfer.getCurrency().isEmpty()) {
            throw new ChaincodeException("币种不能为空");
        }
        if (transfer.getFee() == null) {
            transfer.setFee(BigDecimal.ZERO);
        }
    }

    private void validateAccountExists(ChaincodeStub stub, String accountId) {
        String accountKey = ACCOUNT_PREFIX + accountId;
        String accountJson = stub.getStringState(accountKey);
        if (accountJson == null || accountJson.isEmpty()) {
            throw new ChaincodeException("账户不存在: " + accountId);
        }

        AccountEntity account = JSON.parseObject(accountJson, AccountEntity.class);
        if (!account.isStatus()) {
            throw new ChaincodeException("账户已冻结: " + accountId);
        }
    }

    private AccountBalanceEntity getAccountBalance(ChaincodeStub stub, String balanceKey) {
        String balanceJson = stub.getStringState(balanceKey);
        if (balanceJson == null || balanceJson.isEmpty()) {
            throw new ChaincodeException("账户余额不存在: " + balanceKey);
        }
        return JSON.parseObject(balanceJson, AccountBalanceEntity.class);
    }
}

