package com.simnectz.blockchain.sample.utils;

import lombok.Data;

/**
 * ResultUtil 类用于封装智能合约的返回结果
 * <p>
 * 该类采用通用的响应格式，包含状态码、消息、数据和时间戳
 * 支持泛型，可以封装任意类型的数据
 * <p>
 * 状态码规范：
 * - 20000: 操作成功
 * - 40000: 操作失败，包含各种错误情况
 *
 * @param <T> 数据的类型参数
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
public class ResultUtil<T> {

    /**
     * 状态码
     * 20000表示成功
     */
    private Integer code;

    /**
     * 响应消息
     * 用于描述操作结果或错误原因
     */
    private String message;

    /**
     * 响应数据
     * 可以是任意类型，由泛型参数T决定
     */
    private T data;

    /**
     * 时间戳
     * 记录响应生成的时间，通常使用交易的时间戳
     */
    private Long timestamp;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 构造函数，用于创建没有数据的响应结果
     *
     * @param code 状态码
     * @param message 响应消息
     * @param timestamp 时间戳
     */
    public ResultUtil(Integer code, String message, Long timestamp, String transactionId) {
        this.code = code;
        this.message = message;
        this.timestamp = timestamp;
        this.transactionId = transactionId;
    }

    /**
     * 构造函数，用于创建包含数据的响应结果
     *
     * @param code 状态码
     * @param message 响应消息
     * @param data 响应数据
     * @param timestamp 时间戳
     */
    public ResultUtil(Integer code, String message, T data, Long timestamp) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = timestamp;
    }

}
