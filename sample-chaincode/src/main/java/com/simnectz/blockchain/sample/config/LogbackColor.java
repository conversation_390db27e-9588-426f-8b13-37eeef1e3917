package com.simnectz.blockchain.sample.config;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.pattern.color.ANSIConstants;
import ch.qos.logback.core.pattern.color.ForegroundCompositeConverterBase;

/**
 * LogbackColor 类用于自定义日志输出的颜色配置
 * <p>
 * 该类继承自 ForegroundCompositeConverterBase，用于根据日志级别设置不同的前景色
 * 可以在 logback.xml 配置文件中引用此转换器来实现彩色日志输出
 * <p>
 * 颜色映射关系：
 * - ERROR: 红色
 * - WARN: 黄色
 * - INFO: 蓝色
 * - DEBUG: 绿色
 * - 其他: 默认颜色
 *
 * <AUTHOR>
 * @version 1.0
 */
public class LogbackColor extends ForegroundCompositeConverterBase<ILoggingEvent> {

    /**
     * 根据日志事件的级别返回对应的前景色代码
     *
     * @param event 日志事件对象，包含日志级别等信息
     * @return 返回对应日志级别的ANSI颜色代码
     */
    @Override
    protected String getForegroundColorCode(ILoggingEvent event) {
        Level level = event.getLevel();
        switch (level.toInt()) {
            case Level.ERROR_INT:
                return ANSIConstants.RED_FG;    // 错误级别使用红色
            case Level.WARN_INT:
                return ANSIConstants.YELLOW_FG; // 警告级别使用黄色
            case Level.INFO_INT:
                return ANSIConstants.BLUE_FG;   // 信息级别使用蓝色
            case Level.DEBUG_INT:
                return ANSIConstants.GREEN_FG;  // 调试级别使用绿色
            default:
                return ANSIConstants.DEFAULT_FG; // 其他级别使用默认颜色
        }
    }

}
